/*
	This is the Obsidian example vault is amazing, there are a lot of dazzling features and showcase, I believe you will wonder a bit, is this Obsidian?
	[Blue-topaz-examples](https://github.com/cumany/Blue-topaz-examples)
	*/

var Le=Object.create;var _=Object.defineProperty,Ae=Object.defineProperties,ke=Object.getOwnPropertyDescriptor,Me=Object.getOwnPropertyDescriptors,Oe=Object.getOwnPropertyNames,K=Object.getOwnPropertySymbols,De=Object.getPrototypeOf,X=Object.prototype.hasOwnProperty,Fe=Object.prototype.propertyIsEnumerable;var J=(n,e,t)=>e in n?_(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,q=(n,e)=>{for(var t in e||(e={}))X.call(e,t)&&J(n,t,e[t]);if(K)for(var t of K(e))Fe.call(e,t)&&J(n,t,e[t]);return n},$=(n,e)=>Ae(n,Me(e)),Q=n=>_(n,"__esModule",{value:!0});var Ie=(n,e)=>{Q(n);for(var t in e)_(n,t,{get:e[t],enumerable:!0})},_e=(n,e,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Oe(e))!X.call(n,i)&&i!=="default"&&_(n,i,{get:()=>e[i],enumerable:!(t=ke(e,i))||t.enumerable});return n},V=n=>_e(Q(_(n!=null?Le(De(n)):{},"default",n&&n.__esModule&&"default"in n?{get:()=>n.default,enumerable:!0}:{value:n,enumerable:!0})),n);var H=(n,e,t)=>new Promise((i,o)=>{var a=r=>{try{s(t.next(r))}catch(d){o(d)}},l=r=>{try{s(t.throw(r))}catch(d){o(d)}},s=r=>r.done?i(r.value):Promise.resolve(r.value).then(a,l);s((t=t.apply(n,e)).next())});Ie(exports,{default:()=>Y,refresh_node:()=>He,selfDestruct:()=>I});var C=V(require("obsidian"));var w=V(require("obsidian"));function B(n,e){return n+1<e.length?e[n+1].level>e[n].level:!1}function W(n,e,t){let i,o;n instanceof MouseEvent?(n.stopPropagation(),i=e,o=t||!1):(i=n,o=e);let a=i.getAttribute("isCollapsed");a!==null&&(a==="true"?Be(i,o):a==="false"&&Ne(i))}function Be(n,e){n.setAttribute("isCollapsed","false");let t=parseInt(n.getAttribute("data-level")),i=n.nextElementSibling;if(e)for(;i&&parseInt(i.getAttribute("data-level"))>t;)i.style.display="block",i.getAttribute("isCollapsed")!==null&&i.setAttribute("isCollapsed","false"),i=i.nextElementSibling;else{let o=!1,a=Number.MAX_VALUE;for(;i&&parseInt(i.getAttribute("data-level"))>t;){let l=i.getAttribute("isCollapsed")!==null,s=parseInt(i.getAttribute("data-level"));o?s<=a&&(i.style.display="block",o=l,a=l?s:Number.MAX_VALUE):(l&&(o=!0,a=s),i.style.display="block"),i=i.nextElementSibling}}}function Ne(n){n.setAttribute("isCollapsed","true");let e=parseInt(n.getAttribute("data-level")),t=n.nextElementSibling;for(;t&&parseInt(t.getAttribute("data-level"))>e;)t.style.display="none",t.getAttribute("isCollapsed")!==null&&t.setAttribute("isCollapsed","true"),t=t.nextElementSibling}function Z(n,e){n.querySelectorAll("li.heading-list-item[iscollapsed]").forEach(i=>{let o=i,a=o.getAttribute("isCollapsed")==="true";e!==!a&&W(o,e)})}function Ve(n,e,t,i,o,a){return H(this,null,function*(){let l=/^(?:\s*)[0-9]+\.\s/,s=/^(?:\s*)[\-\+]\s/,r,d="";(r=l.exec(t))!==null?(d=r[0],t=t.replace(l,"")):(r=s.exec(t))!==null&&(d=r[0],t=t.replace(s,""));let g=Number(i.parentElement.getAttribute("data-id")),f=Number(i.parentElement.getAttribute("data-level")),p=u=>{u.stopImmediatePropagation(),W(u,i.parentElement,n.settings.expandAllSubheadings)};i.parentElement.addEventListener("click",p),i.parentElement.hasAttribute("isCollapsed")?B(g,n.headingdata)||(i.parentElement.removeAttribute("isCollapsed"),i.parentElement.removeEventListener("click",p)):B(g,n.headingdata)&&i.parentElement.setAttribute("isCollapsed","false");let m=i;a=new w.Component,yield w.MarkdownRenderer.renderMarkdown(t,m,o,a),m&&m.classList.add("heading-rendered");let T=m.createEl("a");T.addClass("text"),T.onclick=function(u){var M;u.stopPropagation();let S=(M=parseInt(m.parentElement.getAttribute("data-line")))!=null?M:0;if(u.ctrlKey||u.metaKey)qe(e,S);else{Pe(e,S);let x=m.parentElement.parentElement.querySelector(".text-wrap.located");x&&x.removeClass("located"),m.addClass("located")}};let y=m.querySelector("p");if(y){let u=/<a[^>]*>|<\/[^>]*a>/gm;d?T.innerHTML=d+y.innerHTML.replace(u,""):T.innerHTML=y.innerHTML.replace(u,""),m.removeChild(y),n.settings.isTooltip&&(m.setAttribute("aria-label",t),n.settings.positionStyle=="right"&&m.setAttribute("aria-label-position","left"),n.settings.positionStyle=="left"&&m.setAttribute("aria-label-position","right"),n.settings.positionStyle=="both"&&m.setAttribute("aria-label-position","top"))}})}function N(n,e,t,i,o){return H(this,null,function*(){var r,d,g,f,p,m;if(!i)return;let a=t.createEl("li");a.addClass("heading-list-item"),a.setAttribute("data-level",(d=(r=i==null?void 0:i.level)==null?void 0:r.toString())!=null?d:""),a.setAttribute("data-id",o.toString()),a.setAttribute("data-line",(m=(p=(f=(g=i==null?void 0:i.position)==null?void 0:g.start)==null?void 0:f.line)==null?void 0:p.toString())!=null?m:"");let l=a.createEl("div");l.addClass("text-wrap"),yield Ve(n,e,i.heading,l,e.file.path,null);let s=a.createEl("div");s.addClass("line-wrap"),s.createDiv().addClass("line")})}var Pe=(n,e)=>{n.leaf.openFile(n.file,{eState:{line:e}})},qe=(n,e)=>{var a,l;let t=(l=(a=n==null?void 0:n.currentMode.getFoldInfo())==null?void 0:a.folds)!=null?l:[],i=e,o=0;if(t.some((s,r)=>(o=r,s.from==i)))t.splice(o,1);else{let s={from:e,to:e+1};t.push(s)}n==null||n.currentMode.applyFoldInfo({folds:t,lines:n.editor.lineCount()}),n==null||n.onMarkdownFold()};function P(n,e){var o;let t=(a,l)=>{var m,T,y;let s=n.workspace.getActiveFile(),r=n.metadataCache.getFileCache(s).headings,d=[];if(r==null||r.map(u=>{u.heading=u.heading.replace(/<\/?[\s\S]*?(?:".*")*>/g,""),d.push(u)}),e.headingdata=d,e.headingdata.length==0)return;e.settings.positionStyle=="right"?(l.addClass("floating-right"),l.removeClass("floating-left"),l.removeClass("floating-both")):e.settings.positionStyle=="left"?(l.addClass("floating-left"),l.removeClass("floating-rigth"),l.removeClass("floating-both")):e.settings.positionStyle=="both"&&(l.addClass("floating-both"),l.removeClass("floating-left"),l.removeClass("floating-rigth")),e.settings.isLeft?(l.removeClass("alignLeft"),l.addClass("alignLeft")):l.removeClass("alignLeft");let g=l.createEl("ul");g.addClass("floating-toc");let f=l.createEl("div");if(R(e,f,l),e.settings.ignoreHeaders){let u=e.settings.ignoreHeaders.split(`
`);e.headingdata=(m=n.metadataCache.getFileCache(s).headings)==null?void 0:m.filter(S=>!u.includes(S.level.toString()))}(()=>{let u=e.headingdata.length,S=n.workspace.getActiveViewOfType(w.MarkdownView);if(u>50){let M=20,x=e.headingdata.slice(0,M),E=activeDocument.createElement("div");E.className="toc-loading-indicator",E.textContent=`Loading... (${M}/${u})`,E.style.textAlign="center",E.style.padding="8px",E.style.color="var(--text-muted)",E.style.fontSize="0.8em",E.style.position="fixed",E.style.top="45px",x.forEach((h,c)=>{N(e,S,g,h,c)}),g.appendChild(E);let O=M,F=20,D=()=>{let h=Math.min(O+F,u);E.textContent=`\u52A0\u8F7D\u4E2D... (${h}/${u})`;for(let c=O;c<h;c++)N(e,S,g,e.headingdata[c],c);O=h,O<u?requestAnimationFrame(()=>{setTimeout(D,10)}):E.remove()};setTimeout(D,50)}else e.headingdata.forEach((M,x)=>{N(e,S,g,M,x)})})(),((T=a==null?void 0:a.querySelector(".markdown-source-view"))==null?void 0:T.insertAdjacentElement("beforebegin",l))||((y=a==null?void 0:a.querySelector(".markdown-reading-view"))==null||y.insertAdjacentElement("beforebegin",l))};if(this.app.workspace.getActiveViewOfType(w.MarkdownView)){(0,w.requireApiVersion)("0.15.0")?activeDocument=activeWindow.document:activeDocument=window.document;let a=e.app.workspace.getActiveViewOfType(w.MarkdownView);if(a){if((o=a.contentEl)==null?void 0:o.querySelector(".floating-toc-div"))return;{let s=createEl("div");s.addClass("floating-toc-div"),e.BAR_STYLE_CLASSES.forEach(r=>{activeDocument.body.removeClass(r)}),e.settings.isDefaultPin&&s.addClass("pin"),e.settings.isDefaultHide&&s.addClass("hide"),e.settings.enableHeadingNowrap&&activeDocument.body.addClass("enable-heading-nowrap"),e.settings.enableBarHeadingText&&activeDocument.body.addClass("enable-bar-heading-text"),activeDocument.body.addClass(e.settings.barStyle||"enable-edge-style"),t(a.contentEl,s),e.updateTocWidth(s,e.headingdata)}}}}function R(n,e,t){e.addClass("toolbar"),e.addClass("hide"),new w.ButtonComponent(e).setIcon("pin").setTooltip("pin").onClick(()=>{t.classList.contains("pin")?t.removeClass("pin"):t.addClass("pin")}),new w.ButtonComponent(e).setIcon("double-up-arrow-glyph").setTooltip("Scroll to Top").setClass("top").onClick(()=>{let r=this.app.workspace.getActiveViewOfType(w.MarkdownView);r&&r.setEphemeralState({scroll:0})}),new w.ButtonComponent(e).setIcon("double-down-arrow-glyph").setTooltip("Scroll to Bottom").setClass("bottom").onClick(()=>H(this,null,function*(){let r=this.app.workspace.getActiveViewOfType(w.MarkdownView);if(r){let d=this.app.workspace.getActiveFile(),f=(yield this.app.vault.cachedRead(d)).split(`
`),p=f.length;if(r.getMode()==="preview")for(;p>0&&f[p-1].trim()==="";)p--;r.currentMode.applyScroll(p-1)}})),new w.ButtonComponent(e).setIcon("copy").setTooltip("copy TOC to clipboard").setClass("copy").onClick(()=>H(this,null,function*(){let r=n.headingdata.map(d=>`${"    ".repeat(d.level-1)}- [[#${d.heading}]]`);yield navigator.clipboard.writeText(r.join(`
`)),new w.Notice("TOC Copied")}));let s=new w.ButtonComponent(e).setIcon("chevron-down").setTooltip("Collapse/Expand all headings").setClass("toggle-all").onClick(()=>{let r=t.getAttribute("data-all-expanded")==="true";s.setIcon(r?"chevron-right":"chevron-down"),Z(t,!r),t.setAttribute("data-all-expanded",(!r).toString())})}var L=V(require("obsidian"));var ee=["left","right","both"],te={ignoreHeaders:"",ignoreTopHeader:!1,positionStyle:"left",isLoadOnMobile:!0,isLeft:!1,isDefaultPin:!1,isTooltip:!0,defaultCollapsedLevel:6,expandAllSubheadings:!1,isDefaultHide:!1,enableHeadingNowrap:!0,barStyle:"enable-edge-style",enableBarHeadingText:!1};var Se=V(require("obsidian"));var ie={};var ne={};var ae={};var le={};var z={"ctrl + click on the floating toc to collapse/expand the header.":"ctrl + click on the floating toc to collapse/expand the header.","Floating TOC position":"Floating TOC position","Floating TOC position, default on the left side of the notes":"Floating TOC position, default on the left side of the notes","Hide heading level":"Hide heading level","Whichever option is selected, the corresponding heading level will be hidden":"Whichever option is selected, the corresponding heading level will be hidden","Plugin Settings":"Plugin Settings","Default Pin":"Default Pin","Enable Tooltip":"Enable Tooltip","Plugin Style Settings":"Plugin Style Settings","Mobile enabled or not":"Mobile enabled or not","Whether to enable the plugin for the mobile client, the default is enabled.":"Whether to enable the plugin for the mobile client, the default is enabled.","If the floating Toc option is not found in the style setting, please reload the style setting plugin (turn it off and on again)":"If the floating Toc option is not found in the style setting, please reload the style setting plugin (turn it off and on again)","Left alignment of TOC text":"Left alignment of TOC text","Aligned on both sides":"Aligned on both sides","Floating TOC position, on the right side of the notes":"Floating TOC position, on the right side of the notes","whether the text in TOC is left aligned":"whether the text in TOC is left aligned","When the panel is split left and right, the right side of the layout is aligned right and the left side of the panel is aligned left.":"When the panel is split left and right, the right side of the layout is aligned right and the left side of the panel is aligned left.","Set the default collapsed level of headings when initialised":"Set the default collapsed level of headings when initialised","Default Collapsed Level":"Default Collapsed Levels","Expand All Subheadings Recursively":"Expand All Subheadings Recursively","When disabled, only direct subheadings will be expanded":"When disabled, only direct subheadings will be expanded","Basic Settings":"Basic Settings","TOC Display Settings":"TOC Display Settings","Interaction Settings":"Interaction Settings","Style Settings":"Style Settings","Default Hide TOC":"Default Hide TOC","When enabled, TOC will be hidden by default when plugin starts":"When enabled, TOC will be hidden by default when plugin starts","Header single line display":"Header single line display","When enabled, heading text will be displayed in a single line":"When enabled, heading text will be displayed in a single line","Indicator bar style":"Indicator bar style","Choose the style of the indicator bar":"Choose the style of the indicator bar",Default:"Default",Icon:"Icon",Bold:"Bold","Show heading text next to indicator bar":"Show heading text next to indicator bar","When enabled, heading text will be shown next to the indicator bar":"When enabled, heading text will be shown next to the indicator bar","More Style Settings":"More Style Settings","Notice: Please click the button again,If the floating-toc option is not found in the style settings":"Notice: Please click the button again,If the floating-toc option is not found in the style settings"};var oe={};var se={};var re={};var de={};var ce={};var he={};var ge={};var pe={};var fe={};var ue={};var me={};var be={};var ve={};var ye={};var Ce={};var we={};var Ee={"ctrl + click on the floating toc to collapse/expand the header.":"\u6309\u4F4Fctrl \u70B9\u51FB\u76EE\u5F55\u4E2D\u7684\u6807\u9898\uFF0C\u53EF\u4EE5\u4F7F\u5BF9\u5E94\u7684\u6B63\u6587\u5185\u5BB9\u6298\u53E0/\u5C55\u5F00\u3002","Floating TOC position":"\u6D6E\u52A8\u76EE\u5F55\u663E\u793A\u4F4D\u7F6E","Floating TOC position, default on the left side of the notes":"\u6D6E\u52A8\u76EE\u5F55\u663E\u793A\u4F4D\u7F6E\uFF0C\u9ED8\u8BA4\u663E\u793A\u5728\u7B14\u8BB0\u5DE6\u4FA7","Hide heading level":"\u9690\u85CF\u6307\u5B9A\u7684\u6807\u9898\u5C42\u7EA7","Whichever option is selected, the corresponding heading level will be hidden":"\u9690\u85CF\u9009\u4E2D\u7684\u6807\u9898\u5C42\u7EA7\uFF0C\u9009\u4E2D\u7684\u6807\u9898\u4E0D\u4F1A\u5728\u6D6E\u52A8\u76EE\u5F55\u4E2D\u663E\u793A\u3002","Plugin Settings":"\u63D2\u4EF6\u8BBE\u7F6E","Default Pin":"\u662F\u5426\u9ED8\u8BA4\u9489\u5728\u7B14\u8BB0\u4E0A","Enable Tooltip":"\u662F\u5426\u5F00\u542F\u6807\u9898\u63D0\u793A","Plugin Style Settings":"\u63D2\u4EF6\u6837\u5F0F\u8BBE\u7F6E","Mobile enabled or not":"\u662F\u5426\u5728\u79FB\u52A8\u7AEF\u542F\u7528","Whether to enable the plugin for the mobile client, the default is enabled.":"\u79FB\u52A8\u5BA2\u6237\u7AEF\u662F\u5426\u542F\u7528\u63D2\u4EF6\uFF0C\u9ED8\u8BA4\u542F\u7528\u3002","If the floating Toc option is not found in the style setting, please reload the style setting plugin (turn it off and on again)":"\u5982\u679Cstyle setting \u4E2D\u65E0\u6CD5\u770B\u5230 floating Toc\u9009\u9879\uFF0C\u8BF7\u91CD\u8F7Dstyle setting\u63D2\u4EF6\uFF08\u5173\u95ED\u518D\u5F00\u542F\u5373\u53EF\uFF09","Left alignment of TOC text":"\u76EE\u5F55\u6587\u5B57\u5DE6\u5BF9\u9F50","Floating TOC position, on the right side of the notes":"\u6D6E\u52A8\u76EE\u5F55\u663E\u793A\u4F4D\u7F6E\uFF0C\u663E\u793A\u5728\u7B14\u8BB0\u53F3\u4FA7","whether the text in TOC is left aligned":"\u5F53\u5DE5\u5177\u680F\u5728\u53F3\u4FA7\u65F6\uFF0C\u76EE\u5F55\u4E2D\u7684\u6807\u9898\u662F\u5426\u5DE6\u5BF9\u9F50","Aligned on both sides":"\u4E24\u7AEF\u5BF9\u9F50","When the panel is split left and right, the right side of the layout is aligned right and the left side of the panel is aligned left.":"\u5F53\u9762\u677F\u5DE6\u53F3\u5206\u5272\u7684\u65F6\u5019\uFF0C\u53F3\u4FA7\u7248\u9762\u53F3\u5BF9\u9F50\uFF0C\u5DE6\u4FA7\u9762\u677F\u5DE6\u5BF9\u9F50\u3002","Set the default collapsed level of headings when initialised":"\u8BBE\u7F6E\u521D\u59CB\u5316\u65F6TOC\u4E2D\u9ED8\u8BA4\u6298\u53E0\u7684\u6807\u9898\u7EA7\u522B","Default Collapsed Level":"\u9ED8\u8BA4\u6298\u53E0\u7EA7\u522B","Expand All Subheadings Recursively":"\u9012\u5F52\u5C55\u5F00\u6240\u6709\u5B50\u6807\u9898","When disabled, only direct subheadings will be expanded":"\u5173\u95ED\u6B64\u9009\u9879\u65F6, \u53EA\u5C55\u5F00\u76F4\u63A5\u5B50\u6807\u9898","Basic Settings":"\u57FA\u672C\u8BBE\u7F6E","TOC Display Settings":"\u76EE\u5F55\u663E\u793A\u8BBE\u7F6E","Interaction Settings":"\u4EA4\u4E92\u8BBE\u7F6E","Style Settings":"\u6837\u5F0F\u8BBE\u7F6E","Default Hide TOC":"\u9ED8\u8BA4\u9690\u85CF\u76EE\u5F55","When enabled, TOC will be hidden by default when plugin starts":"\u542F\u7528\u540E\uFF0C\u63D2\u4EF6\u542F\u52A8\u65F6\u76EE\u5F55\u5C06\u9ED8\u8BA4\u9690\u85CF","Header single line display":"\u76EE\u5F55\u6807\u9898\u5355\u884C\u663E\u793A","When enabled, heading text will be displayed in a single line":"\u542F\u7528\u540E\uFF0C\u76EE\u5F55\u6807\u9898\u5C06\u5728\u4E00\u884C\u5185\u663E\u793A","Indicator bar style":"\u6307\u793A\u6761\u6837\u5F0F","Choose the style of the indicator bar":"\u9009\u62E9\u6307\u793A\u6761\u7684\u6837\u5F0F",Default:"\u9ED8\u8BA4",Icon:"\u56FE\u6807",Bold:"\u7C97\u4F53","Show heading text next to indicator bar":"\u5728\u6307\u793A\u6761\u65C1\u8FB9\u663E\u793A\u6807\u9898\u4E0A\u4E0B\u7EA7","When enabled, heading text will be shown next to the indicator bar":"\u542F\u7528\u540E\uFF0C\u6807\u9898\u6587\u672C\u5C06\u663E\u793A\u5728\u6307\u793A\u6761\u65C1\u8FB9","More Style Settings":"\u66F4\u591A\u6837\u5F0F\u8BBE\u7F6E"};var Te={"Floating TOC position":"\u6D6E\u52D5\u76EE\u9304\u986F\u793A\u4F4D\u7F6E","Floating TOC position, default on the left side of the notes":"\u6D6E\u52D5\u76EE\u9304\u986F\u793A\u4F4D\u7F6E\uFF0C\u9ED8\u8A8D\u986F\u793A\u5728\u7B46\u8A18\u5DE6\u5074","Ignore top-level headers":"\u662F\u5426\u5FFD\u7565\u9802\u7D1A\u76EE\u9304","Select whether to ignore the top-level headings. When turned on, the top-level headings in the current note are not displayed in the floating TOC.":"\u9078\u64C7\u662F\u5426\u5FFD\u7565\u9802\u7D1A\u6A19\u984C\uFF0C\u958B\u555F\u5F8C\u7576\u524D\u6587\u6A94\u4E2D\u6700\u9802\u7D1A\u7684\u6A19\u984C\u4E0D\u986F\u793A\u5728\u6D6E\u52D5\u76EE\u9304\u4E2D\u3002","Plugin Settings":"\u63D2\u4EF6\u8A2D\u7F6E","Default Pin":"\u662F\u5426\u9ED8\u8A8D\u91D8\u5728\u7B46\u8A18\u4E0A","Plugin Style Settings":"\u63D2\u4EF6\u6A23\u5F0F\u8A2D\u7F6E","Mobile enabled or not":"\u662F\u5426\u5728\u79FB\u52D5\u7AEF\u555F\u7528","Whether to enable the plugin for the mobile client, the default is enabled.":"\u79FB\u52D5\u5BA2\u6236\u7AEF\u662F\u5426\u555F\u7528\u63D2\u4EF6\uFF0C\u9ED8\u8A8D\u555F\u7528\u3002","If the floating Toc option is not found in the style setting, please reload the style setting plugin (turn it off and on again)":"\u5982\u679Cstyle setting \u4E2D\u7121\u6CD5\u770B\u5230 floating Toc\u9078\u9805\uFF0C\u8ACB\u91CD\u8F09style setting\u63D2\u4EF6\uFF08\u95DC\u9589\u518D\u958B\u555F\u5373\u53EF\uFF09","Left alignment of TOC text":"\u76EE\u9304\u6587\u5B57\u5DE6\u5C0D\u9F4A","Floating TOC position, on the right side of the notes":"\u6D6E\u52D5\u76EE\u9304\u986F\u793A\u4F4D\u7F6E\uFF0C\u986F\u793A\u5728\u7B46\u8A18\u53F3\u5074","whether the text in TOC is left or right aligned When the floating toc is on the right":"\u7576\u5DE5\u5177\u6B04\u5728\u53F3\u5074\u6642\uFF0C\u76EE\u9304\u4E2D\u7684\u6A19\u984C\u662F\u5426\u5DE6\u5C0D\u9F4A","Aligned on both sides":"\u5169\u7AEF\u5C0D\u9F4A","When the panel is split left and right, the right side of the layout is aligned right and the left side of the panel is aligned left.":"\u7576\u9762\u677F\u5DE6\u53F3\u5206\u5272\u7684\u6642\u5019\uFF0C\u53F3\u5074\u7248\u9762\u53F3\u5C0D\u9F4A\uFF0C\u5DE6\u5074\u9762\u677F\u5DE6\u5C0D\u9F4A\u3002"};var $e={ar:ie,cs:ne,da:ae,de:le,en:z,"en-gb":oe,es:se,fr:re,hi:de,id:ce,it:he,ja:ge,ko:pe,nl:fe,nn:ue,pl:me,pt:be,"pt-br":ve,ro:ye,ru:Ce,tr:we,"zh-cn":Ee,"zh-tw":Te},xe=$e[Se.moment.locale()];function v(n){return xe&&xe[n]||z[n]}var j=class{constructor(e){this.checkedList=[];this.containerEl=e,this.flowListEl=this.containerEl.createDiv({cls:"check-list"})}addItem(e,t,i,o){let a=this.flowListEl.createDiv({cls:"check-item"}),l=a.createEl("input",{type:"checkbox"});return l.checked=i,l.checked&&this.checkedList.push(t),l.addEventListener("change",r=>{l.checked?this.checkedList.includes(t)||this.checkedList.push(t):this.checkedList.includes(t)&&this.checkedList.remove(t)}),l.addEventListener("change",r=>o(l.checked)),a.createDiv({cls:"flow-label"}).setText(e),a}};var U=class extends L.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t,addEventListener("refresh-toc",()=>{I(),P(e,this.plugin)})}display(){var F,D;let{containerEl:e}=this;e.empty(),e.createEl("h1",{text:"Obsidian Floating TOC "}),e.createEl("span",{text:""}).createEl("a",{text:"Author: Cuman \u2728",href:"https://github.com/cumany"}),e.createEl("span",{text:""}).createEl("a",{text:"Readme:\u4E2D\u6587",href:"https://pkmer.cn/Pkmer-Docs/10-obsidian/obsidian%E7%A4%BE%E5%8C%BA%E6%8F%92%E4%BB%B6/floating-toc/"}),e.createEl("span",{text:""}).createEl("a",{text:"|English  ",href:"https://github.com/cumany/obsidian-floating-toc-plugin/blob/master/README.md"});let t=e.createEl("div");t.addClass("callout"),t.setAttribute("data-callout","info");let i=t.createEl("div",{text:"\u{1F511}TIPS:"});i.addClass("callout-title"),i.createEl("br"),t.createEl("div",{text:v("ctrl + click on the floating toc to collapse/expand the header.")}).addClass("callout-content");let a=e.createEl("div",{cls:"floating-toc-tabs"}),l=a.createEl("div",{cls:"floating-toc-tab-header"}),s=a.createEl("div",{cls:"floating-toc-tab-content"}),r=["\u{1F3A2}TOC Display","\u{1F3AE}Interaction","\u{1F3A8}Style Settings"],d={};r.forEach(h=>{let c=l.createEl("div",{cls:"floating-toc-tab"});c.setText(h),c.addEventListener("click",()=>{l.querySelectorAll(".floating-toc-tab").forEach(A=>A.removeClass("active")),s.querySelectorAll(".floating-toc-tab-pane").forEach(A=>A.removeClass("active")),c.addClass("active"),d[h].addClass("active")});let b=s.createEl("div",{cls:"floating-toc-tab-pane"});d[h]=b}),(F=l.querySelector(".floating-toc-tab"))==null||F.addClass("active"),(D=s.querySelector(".floating-toc-tab-pane"))==null||D.addClass("active");let g=d["\u{1F3A2}TOC Display"];g.createEl("h2",{text:v("TOC Display Settings")}),new L.Setting(g).setName(v("Floating TOC position")).setDesc(this.plugin.settings.positionStyle=="both"?v("When the panel is split left and right, the right side of the layout is aligned right and the left side of the panel is aligned left."):this.plugin.settings.positionStyle=="right"?v("Floating TOC position, on the right side of the notes"):v("Floating TOC position, default on the left side of the notes")).addDropdown(h=>{let c={};ee.map(b=>c[b]=b),h.addOptions(c),h.setValue(this.plugin.settings.positionStyle).onChange(b=>{this.plugin.settings.positionStyle=b,this.plugin.saveSettings(),setTimeout(()=>{this.display(),dispatchEvent(new Event("refresh-toc"))},100)})}),this.plugin.settings.positionStyle!="left"&&new L.Setting(g).setName(v("Left alignment of TOC text")).setDesc(v("whether the text in TOC is left aligned")).addToggle(h=>{var c;return h.setValue((c=this.plugin.settings)==null?void 0:c.isLeft).onChange(b=>{this.plugin.settings.isLeft=b,this.plugin.saveSettings(),setTimeout(()=>{this.display(),dispatchEvent(new Event("refresh-toc"))},100)})}),new L.Setting(g).setName(v("Default Hide TOC")).setDesc(v("When enabled, TOC will be hidden by default when plugin starts")).addToggle(h=>{var c;return h.setValue((c=this.plugin.settings)==null?void 0:c.isDefaultHide).onChange(b=>{this.plugin.settings.isDefaultHide=b,this.plugin.saveSettings(),setTimeout(()=>{dispatchEvent(new Event("refresh-toc"))},100)})}),new L.Setting(g).setName(v("Expand All Subheadings Recursively")).setDesc(v("When disabled, only direct subheadings will be expanded")).addToggle(h=>h.setValue(this.plugin.settings.expandAllSubheadings).onChange(c=>{this.plugin.settings.expandAllSubheadings=c,this.plugin.saveSettings(),setTimeout(()=>{dispatchEvent(new Event("refresh-toc"))},100)})),new L.Setting(g).setName(v("Hide heading level")).setDesc(v("Whichever option is selected, the corresponding heading level will be hidden"));let p=new j(g);[1,2,3,4,5,6].forEach(h=>H(this,null,function*(){let b=this.plugin.settings.ignoreHeaders.split(`
`).includes(h.toString());p.addItem(h.toString(),h.toString(),b,A=>{this.plugin.settings.ignoreHeaders=p.checkedList.join(`
`),this.plugin.saveSettings(),setTimeout(()=>{dispatchEvent(new Event("refresh-toc"))},100)})}));let T=d["\u{1F3AE}Interaction"];T.createEl("h2",{text:v("Interaction Settings")}),new L.Setting(T).setName(v("Default Pin")).addToggle(h=>{var c;return h.setValue((c=this.plugin.settings)==null?void 0:c.isDefaultPin).onChange(b=>{this.plugin.settings.isDefaultPin=b,this.plugin.saveSettings(),setTimeout(()=>{dispatchEvent(new Event("refresh-toc"))},100)})}),new L.Setting(T).setName(v("Enable Tooltip")).addToggle(h=>{var c;return h.setValue((c=this.plugin.settings)==null?void 0:c.isTooltip).onChange(b=>{this.plugin.settings.isTooltip=b,this.plugin.saveSettings(),setTimeout(()=>{dispatchEvent(new Event("refresh-toc"))},100)})});let y=d["\u{1F3A8}Style Settings"];y.createEl("h2",{text:v("Style Settings")}),new L.Setting(y).setName(v("Header single line display")).setDesc(v("When enabled, heading text will be displayed in a single line")).addToggle(h=>{var c;return h.setValue((c=this.plugin.settings)==null?void 0:c.enableHeadingNowrap).onChange(b=>{this.plugin.settings.enableHeadingNowrap=b,this.plugin.saveSettings(),setTimeout(()=>{dispatchEvent(new Event("refresh-toc"))},100)})}),new L.Setting(y).setName(v("Indicator bar style")).setDesc(v("Choose the style of the indicator bar")).addDropdown(h=>{h.addOption("default-bar-style","Default").addOption("enable-edge-style","Edge").addOption("enable-bar-icon","Icon").addOption("enable-bold-bar","Bold").addOption("enable-dot-style","Dot").addOption("enable-square-style","Square").addOption("enable-vertical-line-style","Vertical Line").addOption("enable-hollow-line-style","Hollow Line").setValue(this.plugin.settings.barStyle).onChange(c=>{this.plugin.settings.barStyle=c,this.plugin.saveSettings(),setTimeout(()=>{dispatchEvent(new Event("refresh-toc"))},100)})}),new L.Setting(y).setName(v("Show heading text next to indicator bar")).setDesc(v("When enabled, heading text will be shown next to the indicator bar")).addToggle(h=>{var c;return h.setValue((c=this.plugin.settings)==null?void 0:c.enableBarHeadingText).onChange(b=>{this.plugin.settings.enableBarHeadingText=b,this.plugin.saveSettings(),setTimeout(()=>{dispatchEvent(new Event("refresh-toc"))},100)})}),y.createEl("h2",{text:v("More Style Settings")});let u=y.createEl("div");u.addClass("callout"),u.setAttribute("data-callout","warning");let S=u.createEl("div");S.addClass("callout-content"),this.app.plugins.enabledPlugins.has("obsidian-style-settings")?(S.createEl("br"),new L.ButtonComponent(S).setIcon("palette").setClass("mod-cta").setButtonText("\u{1F3A8} Open style settings").onClick(()=>{this.app.setting.open(),this.app.setting.openTabById("obsidian-style-settings"),this.app.workspace.trigger("parse-style-settings"),setTimeout(()=>{var b,A,G;let c=this.app.setting.activeTab.containerEl.querySelector(".setting-item-heading[data-id='floating-toc-styles']");c?(b=c.addClass)==null||b.call(c,"float-cta"):(this.app.workspace.trigger("parse-style-settings"),(G=(A=this.app.setting.activeTab.containerEl.querySelector(".setting-item-heading[data-id='floating-toc-styles']"))==null?void 0:A.addClass)==null||G.call(A,"float-cta"))},250)})):(S.createEl("br"),S.createEl("span",{text:""}).createEl("a",{text:"Please install or enable the style-settings plugin",href:"obsidian://show-plugin?id=obsidian-style-settings"}));let x=e.createEl("div",{cls:"cDonationSection"}),E=createEl("p"),O=createEl("p");O.appendText("If you like this Plugin and are considering donating to support continued development, use the button below!"),E.setAttribute("style","color: var(--text-muted)"),x.appendChild(O),x.appendChild(E),x.appendChild(We("https://github.com/cumany#thank-you-very-much-for-your-support"))}},We=n=>{let e=createEl("a");return e.setAttribute("href",n),e.addClass("buymeacoffee-img"),e.innerHTML='<img src="https://img.buymeacoffee.com/button-api/?text=Buy me a coffee &emoji=&slug=Cuman&button_colour=BD5FFF&font_colour=ffffff&font_family=Poppins&outline_colour=000000&coffee_colour=FFDD00" />',e};var k;function I(){(0,C.requireApiVersion)("0.15.0")?k=activeWindow.document:k=window.document,k.querySelectorAll(".floating-toc-div").forEach(e=>{e&&e.remove()})}function He(n,e){var g,f;(0,C.requireApiVersion)("0.15.0")?k=activeWindow.document:k=window.document;let t=(g=e.contentEl)==null?void 0:g.querySelector(".floating-toc-div");if(!t)return!1;let i=t.querySelector(".toolbar");i||(i=t.createEl("div"),R(n,i,t));let o=t.querySelector("ul.floating-toc");o||(o=t.createEl("ul"),o.addClass("floating-toc"));let a=n.headingdata;if(n.settings.ignoreHeaders){let p=new Set(n.settings.ignoreHeaders.split(`
`));a=(f=n.headingdata)==null?void 0:f.filter(m=>!p.has(m.level.toString()))}if(!a)return o.remove(),!1;a&&a.length>0&&n.updateTocWidth(t,a);let l=new Map;(t==null?void 0:t.querySelectorAll("li.heading-list-item")).forEach(p=>{let m=`${p.getAttribute("data-level")}-${p.getAttribute("data-line")}-${p.children[0].innerText}`;l.set(m,p)});let r=k.createDocumentFragment(),d=new Set(l.values());return a.forEach((p,m)=>{let T=`${p.level}-${p.position.start.line}-${p.heading}`,y=l.get(T);y?(d.delete(y),B(m,n.headingdata)?y.hasAttribute("iscollapsed")||y.setAttribute("isCollapsed","false"):y.hasAttribute("iscollapsed")&&y.removeAttribute("isCollapsed"),r.appendChild(y)):N(n,e,r,p,m)}),d.forEach(p=>p.remove()),o.replaceChildren(r),!0}function Re(n){let e=[];if(n==null?void 0:n.previousElementSibling)for(;n=n.previousElementSibling;)n.nodeType==1&&e.push(n);return e}function ze(n,e,t){var o,a,l;let i=t.target;if(((o=i.parentElement)==null?void 0:o.classList.contains("cm-editor"))||((a=i.parentElement)==null?void 0:a.classList.contains("markdown-reading-view"))){let s=n.workspace.getActiveViewOfType(C.MarkdownView);if(!s)return;let r=(l=s.currentMode.getScroll())!=null?l:0,d=e.headingdata;if(!d||d.length===0)return;let g=s.contentEl.querySelector(".floating-toc");if(!g)return;let f=g.querySelectorAll("li.heading-list-item");if(!f.length)return;let p=f[0],m=f[f.length-1],T=parseInt(p.getAttribute("data-line")||"0"),y=parseInt(m.getAttribute("data-line")||"0"),u=0,S=null;if(r<=0)u=T;else{let h=0,c=d.length-1,b=-1;for(;h<=c;){let A=Math.floor((h+c)/2);d[A].position.start.line<=r?(b=A,h=A+1):c=A-1}b!==-1?(u=d[b].position.start.line,S=d[b]):u=T}let M=g.querySelector(".heading-list-item.located");M&&M.removeClass("located");let x=g.querySelector(`li[data-line='${u}']`);if(!x)return;x.addClass("located");let E=parseInt(x.getAttribute("data-level")||"1"),O=E>1?E-1:1,F=g.querySelector("li.focus");F&&F.removeClass("focus");let D=Re(x);for(let h=0;h<D.length;h++){let c=D[h];if(c.dataset.level<=O.toString()){c.addClass("focus");break}}requestAnimationFrame(()=>{x.scrollIntoView({block:"nearest",behavior:"smooth"})})}}var Y=class extends C.Plugin{constructor(){super(...arguments);this.isUpdating=!1;this.lastRefreshTime=0;this.REFRESH_COOLDOWN=200;this.currentFile=null;this.BAR_STYLE_CLASSES=["enable-bar-heading-text","enable-heading-nowrap","pin","hide","default-bar-style","enable-edge-style","enable-bar-icon","enable-bold-bar","enable-dot-style","enable-square-style","enable-vertical-line-style","enable-hollow-line-style"];this.updateTocWidth=(0,C.debounce)((e,t)=>{let i=t.reduce((a,l)=>{let r=l.heading.split("").reduce((d,g)=>d+(/[\u4e00-\u9fa5]/.test(g)?1:.6),0);return Math.max(a,r)},0),o=Math.ceil(i)+"rem";k.body.style.setProperty("--actual-toc-width",`${o}`)},100);this.handleScroll=(e,t,i)=>(0,C.debounce)(ze(e,t,i),100)}onload(){return H(this,null,function*(){(0,C.requireApiVersion)("0.15.0")?k=activeWindow.document:k=window.document,yield this.loadSettings();let e=i=>{i&&(He(this,i)||P(this.app,this))};this.addCommand({id:"pin-toc-panel",name:"Pinning the Floating TOC panel",icon:"pin",callback:()=>H(this,null,function*(){let i=this.app.workspace.getActiveViewOfType(C.MarkdownView);if(i){let o=i.contentEl.querySelector(".floating-toc-div");o&&(o.classList.contains("pin")?o.removeClass("pin"):o.addClass("pin"))}})}),this.addCommand({id:"hide-toc-panel",name:"Hide/Show the Floating TOC panel",icon:"list",callback:()=>H(this,null,function*(){let i=this.app.workspace.getActiveViewOfType(C.MarkdownView);if(i){let o=i.contentEl.querySelector(".floating-toc-div");o&&(o.classList.contains("hide")?o.removeClass("hide"):o.addClass("hide"))}})}),this.addCommand({id:"scroll-to-bottom",name:"Scroll to Bottom",icon:"double-down-arrow-glyph",callback:()=>H(this,null,function*(){let i=this.app.workspace.getActiveViewOfType(C.MarkdownView);if(i){let o=this.app.workspace.getActiveFile(),l=(yield this.app.vault.cachedRead(o)).split(`
`),s=l.length;if(i.getMode()==="preview")for(;s>0&&l[s-1].trim()==="";)s--;i.currentMode.applyScroll(s-1)}})}),this.addCommand({id:"scroll-to-top",name:"Scroll to Top",icon:"double-up-arrow-glyph",callback:()=>H(this,null,function*(){let i=this.app.workspace.getActiveViewOfType(C.MarkdownView);i&&i.setEphemeralState({scroll:0})})}),this.addCommand({id:"toggle-position-style",name:"Toggle Floating TOC Position (left/right)",icon:"switch",callback:()=>{this.settings.positionStyle==="left"?this.settings.positionStyle="right":this.settings.positionStyle==="right"?this.settings.positionStyle="left":this.settings.positionStyle==="both"&&new C.Notice("Position style set to both. Toogle position only works when fixed position (left or right) is selected."),this.saveSettings(),dispatchEvent(new Event("refresh-toc"))}}),this.registerEvent(this.app.workspace.on("active-leaf-change",()=>{var s,r,d,g;let i=this.app.workspace.getActiveViewOfType(C.MarkdownView);if(!i)return;let o=(s=i.file)==null?void 0:s.path;if(o===this.currentFile)return;this.currentFile=o;let a=this.app.workspace.getActiveFile();if(!a||!((d=(r=this.app.metadataCache.getFileCache(a))==null?void 0:r.headings)==null?void 0:d.length)){this.headingdata=null,I();return}let l=(g=this.app.metadataCache.getFileCache(a))==null?void 0:g.headings;if(!!l){if(this.headingdata=l,this.settings.ignoreHeaders){let f=this.settings.ignoreHeaders.split(`
`);this.headingdata=l.filter(p=>!f.includes(p.level.toString()))}t(i,!0)}})),this.registerEvent(this.app.metadataCache.on("changed",()=>{var d,g;let i=this.app.workspace.getActiveViewOfType(C.MarkdownView);if(!i||((d=i.file)==null?void 0:d.path)!==this.currentFile)return;let o=i.file,a=(g=this.app.metadataCache.getFileCache(o))==null?void 0:g.headings;if(!(a==null?void 0:a.length)){this.headingdata=null,I();return}let l=a.map(f=>$(q({},f),{heading:this.removeMarkdownSyntax(f.heading)})),s=this.headingdata?this.headingdata.map(f=>$(q({},f),{heading:this.removeMarkdownSyntax(f.heading)})):null;if(this.hasStructuralHeadingChanges(l,s)){if(this.headingdata=a,this.settings.ignoreHeaders){let f=this.settings.ignoreHeaders.split(`
`);this.headingdata=a.filter(p=>!f.includes(p.level.toString()))}t(i,!0)}else this.updateOutlineLineNumbers(i,a)}));let t=(i,o=!1)=>{let a=Date.now();!o&&a-this.lastRefreshTime<this.REFRESH_COOLDOWN||(this.lastRefreshTime=a,e(i))};k.addEventListener("scroll",i=>{this.handleScroll(this.app,this,i)},!0),this.addSettingTab(new U(this.app,this)),e(this.app.workspace.getActiveViewOfType(C.MarkdownView)),this.app.workspace.on("window-open",i=>{console.log("window-open"),i.doc.addEventListener("scroll",o=>{this.handleScroll(this.app,this,o)},!0)}),this.app.workspace.onLayoutReady(()=>{this.app.workspace.trigger("parse-style-settings")})})}removeMarkdownSyntax(e){if(!e)return"";let t=e;return t=t.replace(/\*\*(.*?)\*\*/g,"$1").replace(/__(.*?)__/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/_(.*?)_/g,"$1"),t=t.replace(/`([^`]+)`/g,"$1").replace(/~~(.*?)~~/g,"$1"),t=t.replace(/==(.*?)==/g,"$1"),t=t.replace(/\[(.*?)\]\([^\)]+\)/g,"$1").replace(/\[\[(.*?)(\|.*?)?\]\]/g,"$1"),t=t.replace(/<[^>]+>/g,""),t=t.replace(/^#+\s+/,""),t.trim()}hasHeadingsChanged(e,t){if(!t||e.length!==t.length)return!0;let i=a=>`${a.heading}|${a.level}|${a.position.start.line}`;return!e.every((a,l)=>{let s=t[l],r=i(a),d=i(s);return r===d})}updateOutlineLineNumbers(e,t){var l;let i=(l=e.contentEl)==null?void 0:l.querySelector(".floating-toc-div");if(!i)return;let o=i.querySelectorAll("li.heading-list-item");if(!o.length)return;let a=new Map;t.forEach(s=>{let r=`${this.removeMarkdownSyntax(s.heading)}|${s.level}`;a.set(r,s.position.start.line)}),o.forEach(s=>{let r=s.getAttribute("data-level"),d=s.querySelector(".text-wrap a.text");if(!d)return;let f=`${d.innerText}|${r}`;if(a.has(f)){let p=a.get(f);s.getAttribute("data-line")!==p.toString()&&s.setAttribute("data-line",p.toString())}})}hasStructuralHeadingChanges(e,t){if(!t||e.length!==t.length)return!0;let i=o=>`${this.removeMarkdownSyntax(o.heading)}|${o.level}`;return e.some((o,a)=>{let l=t[a];return i(o)!==i(l)})}onunload(){var e;(0,C.requireApiVersion)("0.15.0")?k=activeWindow.document:k=window.document;try{k.removeEventListener("scroll",t=>{this.handleScroll(this.app,this,t)},!0)}catch(t){console.error("Error removing scroll event listener:",t)}try{let t=this.app.workspace.getActiveViewOfType(C.MarkdownView);if(t){let i=(e=t.contentEl)==null?void 0:e.querySelector(".floating-toc-div");i&&(i.querySelectorAll("li.heading-list-item").forEach(a=>{let l=a.cloneNode(!0);a.parentNode&&a.parentNode.replaceChild(l,a)}),i._tocCleanup&&i._tocCleanup())}}catch(t){console.error("Error cleaning up resources:",t)}I()}setHeadingdata(e){this.headingdata=e}loadSettings(){return H(this,null,function*(){this.settings=Object.assign({},te,yield this.loadData())})}saveSettings(){return H(this,null,function*(){yield this.saveData(this.settings)})}};
