{"themes": {"Default": {"settings": {"codeblock": {"lineNumbers": true, "unwrapLines": true, "wrapLinesActive": false, "curvature": 4}, "gutter": {"highlight": true, "activeLine": false}, "header": {"title": {"textFont": "", "textBold": false, "textItalic": true}, "languageTag": {"display": "none", "textFont": "", "textBold": true, "textItalic": false}, "languageIcon": {"display": "none", "displayColour": true}, "externalReference": {"displayRepository": true, "displayVersion": true, "displayTimestamp": true}, "fontSize": 14, "foldPlaceholder": ""}, "highlights": {"activeCodeblockLine": false, "activeEditorLine": false}, "inline": {"syntaxHighlight": true, "style": true, "fontWeight": 4, "curvature": 6, "paddingVertical": 5, "paddingHorizontal": 5, "marginHorizontal": 0, "titleFontWeight": 8}, "advanced": {"gradientHighlights": false, "gradientHighlightsColourStop": "70%", "languageBorderColour": false, "languageBorderWidth": 5, "iconSize": 28}}, "colours": {"light": {"codeblock": {"backgroundColour": "--code-background", "textColour": "--code-normal"}, "gutter": {"backgroundColour": "--code-background", "textColour": "--text-faint", "activeTextColour": "--text-muted"}, "header": {"backgroundColour": "--code-background", "title": {"textColour": "--code-comment"}, "languageTag": {"backgroundColour": "--code-background", "textColour": "--code-comment"}, "externalReference": {"displayRepositoryColour": "#00FFFF", "displayVersionColour": "#FF00FF", "displayTimestampColour": "#808080"}, "lineColour": "--color-base-30"}, "highlights": {"activeCodeblockLineColour": "--color-base-30", "activeEditorLineColour": "--color-base-20", "defaultColour": "--text-highlight-bg", "alternativeHighlights": {}}, "inline": {"backgroundColour": "--code-background", "textColour": "--code-normal", "activeTextColour": "--code-normal", "titleTextColour": "--code-comment"}, "advanced": {"buttonColour": "--text-muted", "buttonActiveColour": "--text-normal"}}, "dark": {"codeblock": {"backgroundColour": "--code-background", "textColour": "--code-normal"}, "gutter": {"backgroundColour": "--code-background", "textColour": "--text-faint", "activeTextColour": "--text-muted"}, "header": {"backgroundColour": "--code-background", "title": {"textColour": "--code-comment"}, "languageTag": {"backgroundColour": "--code-background", "textColour": "--code-comment"}, "externalReference": {"displayRepositoryColour": "#00FFFF", "displayVersionColour": "#FF00FF", "displayTimestampColour": "#808080"}, "lineColour": "--color-base-30"}, "highlights": {"activeCodeblockLineColour": "--color-base-30", "activeEditorLineColour": "--color-base-20", "defaultColour": "--text-highlight-bg", "alternativeHighlights": {}}, "inline": {"backgroundColour": "--code-background", "textColour": "--code-normal", "activeTextColour": "--code-normal", "titleTextColour": "--code-comment"}, "advanced": {"buttonColour": "--text-muted", "buttonActiveColour": "--text-normal"}}}}, "Solarized": {"settings": {"codeblock": {"lineNumbers": true, "unwrapLines": true, "wrapLinesActive": false, "curvature": 4}, "gutter": {"highlight": true, "activeLine": false}, "header": {"title": {"textFont": "", "textBold": false, "textItalic": true}, "languageTag": {"display": "none", "textFont": "", "textBold": true, "textItalic": false}, "languageIcon": {"display": "none", "displayColour": true}, "externalReference": {"displayRepository": true, "displayVersion": true, "displayTimestamp": true}, "fontSize": 14, "foldPlaceholder": ""}, "highlights": {"activeCodeblockLine": false, "activeEditorLine": false}, "inline": {"syntaxHighlight": true, "style": true, "fontWeight": 4, "curvature": 6, "paddingVertical": 5, "paddingHorizontal": 5, "marginHorizontal": 0, "titleFontWeight": 8}, "advanced": {"gradientHighlights": false, "gradientHighlightsColourStop": "70%", "languageBorderColour": false, "languageBorderWidth": 5, "iconSize": 28}}, "colours": {"light": {"codeblock": {"backgroundColour": "#fdf6e3", "textColour": "#bababa"}, "gutter": {"backgroundColour": "#eee8d5", "textColour": "#6c6c6c", "activeTextColour": "#8c8c8c"}, "header": {"backgroundColour": "#D5CCB4", "title": {"textColour": "#866704"}, "languageTag": {"backgroundColour": "#B8B5AA", "textColour": "#C25F30"}, "externalReference": {"displayRepositoryColour": "#941100", "displayVersionColour": "#ff9300", "displayTimestampColour": "#808080"}, "lineColour": "#EDD489"}, "highlights": {"activeCodeblockLineColour": "#eee8d5", "activeEditorLineColour": "#60460633", "defaultColour": "#E9DFBA", "alternativeHighlights": {}}, "inline": {"backgroundColour": "#fdf6e3", "textColour": "#bababa", "activeTextColour": "#bababa", "titleTextColour": "#C25F30"}, "advanced": {"buttonColour": "--text-muted", "buttonActiveColour": "--text-normal"}}, "dark": {"codeblock": {"backgroundColour": "#002b36", "textColour": "#bababa"}, "gutter": {"backgroundColour": "#073642", "textColour": "#6c6c6c", "activeTextColour": "#4c4c4c"}, "header": {"backgroundColour": "#0a4554", "title": {"textColour": "#dadada"}, "languageTag": {"backgroundColour": "#008080", "textColour": "#000000"}, "externalReference": {"displayRepositoryColour": "#00FFFF", "displayVersionColour": "#9437ff", "displayTimestampColour": "#808080"}, "lineColour": "#46cced"}, "highlights": {"activeCodeblockLineColour": "#073642", "activeEditorLineColour": "#468eeb33", "defaultColour": "#054b5c", "alternativeHighlights": {}}, "inline": {"backgroundColour": "#002b36", "textColour": "#bababa", "activeTextColour": "#bababa", "titleTextColour": "#000000"}, "advanced": {"buttonColour": "--text-muted", "buttonActiveColour": "--text-normal"}}}}}, "selectedTheme": "<PERSON><PERSON><PERSON>", "currentTheme": {"settings": {"codeblock": {"lineNumbers": true, "unwrapLines": true, "wrapLinesActive": false, "curvature": 4}, "gutter": {"highlight": true, "activeLine": false}, "header": {"title": {"textFont": "", "textBold": true, "textItalic": false}, "languageTag": {"display": "always", "textFont": "", "textBold": false, "textItalic": false}, "languageIcon": {"display": "none", "displayColour": true}, "externalReference": {"displayRepository": true, "displayVersion": true, "displayTimestamp": true}, "fontSize": 14, "foldPlaceholder": ""}, "highlights": {"activeCodeblockLine": false, "activeEditorLine": false}, "inline": {"syntaxHighlight": true, "style": true, "fontWeight": 4, "curvature": 6, "paddingVertical": 5, "paddingHorizontal": 5, "marginHorizontal": 0, "titleFontWeight": 8}, "advanced": {"gradientHighlights": false, "gradientHighlightsColourStop": "70%", "languageBorderColour": false, "languageBorderWidth": 5, "iconSize": 28}}, "colours": {"light": {"codeblock": {"backgroundColour": "--code-background", "textColour": "--code-normal"}, "gutter": {"backgroundColour": "--code-background", "textColour": "--text-faint", "activeTextColour": "--text-muted"}, "header": {"backgroundColour": "--code-background", "title": {"textColour": "--code-comment"}, "languageTag": {"backgroundColour": "--code-background", "textColour": "--code-comment"}, "externalReference": {"displayRepositoryColour": "#00FFFF", "displayVersionColour": "#FF00FF", "displayTimestampColour": "#808080"}, "lineColour": "--color-base-30"}, "highlights": {"activeCodeblockLineColour": "--color-base-30", "activeEditorLineColour": "--color-base-20", "defaultColour": "--text-highlight-bg", "alternativeHighlights": {}}, "inline": {"backgroundColour": "--code-background", "textColour": "--code-normal", "activeTextColour": "--code-normal", "titleTextColour": "--code-comment"}, "advanced": {"buttonColour": "--text-muted", "buttonActiveColour": "--text-normal"}}, "dark": {"codeblock": {"backgroundColour": "--code-background", "textColour": "--code-normal"}, "gutter": {"backgroundColour": "--code-background", "textColour": "--text-faint", "activeTextColour": "--text-muted"}, "header": {"backgroundColour": "--code-background", "title": {"textColour": "--code-comment"}, "languageTag": {"backgroundColour": "--code-background", "textColour": "--code-comment"}, "externalReference": {"displayRepositoryColour": "#00FFFF", "displayVersionColour": "#FF00FF", "displayTimestampColour": "#808080"}, "lineColour": "--color-base-30"}, "highlights": {"activeCodeblockLineColour": "--color-base-30", "activeEditorLineColour": "--color-base-20", "defaultColour": "--text-highlight-bg", "alternativeHighlights": {}}, "inline": {"backgroundColour": "--code-background", "textColour": "--code-normal", "activeTextColour": "--code-normal", "titleTextColour": "--code-comment"}, "advanced": {"buttonColour": "--text-muted", "buttonActiveColour": "--text-normal"}}}}, "newTheme": "", "newHighlight": "", "exampleCodeblockParameters": "python title:foo", "exampleCodeblockContent": "print(\"This line is very long and should be used as an example for how the plugin deals with wrapping and unwrapping very long lines given the choice of codeblock parameters and settings.\")\nprint(\"This line is highlighted.\")", "exampleInlineCode": "{python icon title:foo} print(\"This is inline code\")", "decoratePrint": true, "excludedLanguages": "ad-*, reference", "externalReferenceUpdateOnLoad": false, "processedCodeblocksWhitelist": "run-*, include", "redirectLanguages": {}, "version": "1.1.7"}