/* Base colors - TODO: are grey 1 and grey 2 used? */
.theme-dark {
  --accent-h: 219;
  --accent-s: 56%;
  --accent-l: 55%;
  
  --background-primary: #272b34;
  --background-primary-alt: #20242b;
  --background-secondary: #20242b;
  --background-secondary-alt: #1a1e24;
  --background-accent: #000;
  --background-modifier-border: #424958;
  --background-modifier-form-field: rgba(0, 0, 0, 0.3);
  --background-modifier-form-field-highlighted: rgba(0, 0, 0, 0.22);
  --background-modifier-box-shadow: rgba(0, 0, 0, 0.3);
  --background-modifier-success: #539126;
  --background-modifier-error: #3d0000;
  --background-modifier-error-rgb: 61, 0, 0;
  --background-modifier-error-hover: #470000;
  --background-modifier-cover: rgba(0, 0, 0, 0.6);
  --text-accent: #61afef;
  --text-accent-hover: #70bdfc;
  --text-normal: #dcddde;
  --text-muted: #888;
  --text-faint: rgb(81, 86, 99);
  --text-error: #e16d76;
  --text-error-hover: #c9626a;
  --text-highlight-bg: rgba(255, 255, 0, 0.4);
  --text-selection: rgba(0, 122, 255, 0.2);
  --text-on-accent: #dcddde;
  --interactive-normal: #20242b;
  --interactive-hover: #353b47;
  --interactive-accent-hover: hsl(var(--accent-h), calc(var(--accent-s) + 5%), calc(var(--accent-l) - 10%));
  --scrollbar-active-thumb-bg: rgba(255, 255, 255, 0.2);
  --scrollbar-bg: rgba(255, 255, 255, 0.05);
  --scrollbar-thumb-bg: rgba(255, 255, 255, 0.1);
  --panel-border-color: #18191e;
  --gray-1: #5C6370;
  --gray-2: #abb2bf;
  --red: #e06c75;
  --orange: #d19a66;
  --green: #98c379;
  --aqua: #56b6c2;
  --purple: #c678dd;
  --blue: #61afef;
  --yellow: #e5c07b;

  --background-modifier-hover: hsla(var(--accent-h), calc(var(--accent-s) - 35%), var(--accent-l), 0.06);
  --divider-color-hover: #404754;
}

.theme-light {
  --accent-h: 230;
  --accent-s: 83%;
  --accent-l: 64%;
  
  --background-primary: #fafafa;
  --background-primary-alt: #eaeaeb;
  --background-secondary: #eaeaeb;
  --background-secondary-alt: #dbdbdc;
  --background-accent: #fff;
  --background-modifier-border: #dbdbdc;
  --background-modifier-form-field: #fff;
  --background-modifier-form-field-highlighted: #fff;
  --background-modifier-box-shadow: rgba(0, 0, 0, 0.1);
  --background-modifier-success: #A4E7C3;
  --background-modifier-error: #e68787;
  --background-modifier-error-rgb: 230, 135, 135;
  --background-modifier-error-hover: #FF9494;
  --background-modifier-cover: rgba(0, 0, 0, 0.8);
  --text-accent: #1592ff;
  --text-accent-hover: #097add;
  --text-normal: #383a42;
  --text-muted: #8e8e90;
  --text-faint: #999999;
  --text-error: #e75545;
  --text-error-hover: #f86959;
  --text-highlight-bg: rgba(255, 255, 0, 0.4);
  --text-selection: rgba(0, 122, 255, 0.15);
  --text-on-accent: #f2f2f2;
  --interactive-normal: #eaeaeb;
  --interactive-hover: #dbdbdc;
  --interactive-accent-rgb: 21, 146, 255;
  --interactive-accent-hover: hsl(var(--accent-h), calc(var(--accent-s) - 10%), calc(var(--accent-l) - 4%));
  --scrollbar-active-thumb-bg: rgba(0, 0, 0, 0.2);
  --scrollbar-bg: rgba(0, 0, 0, 0.05);
  --scrollbar-thumb-bg: rgba(0, 0, 0, 0.1);
  --panel-border-color: #dbdbdc;
  --gray-1: #383a42;
  --gray-2: #383a42;
  --red: #e75545;
  --green: #4ea24c;
  --blue: #3d74f6;
  --purple: #a625a4;
  --aqua: #0084bc;
  --yellow: #e35649;
  --orange: #986800;
}

.theme-dark, .theme-light {
  --ribbon-background: var(--background-primary);
  --drag-ghost-background: var(--background-secondary-alt);
  --background-modifier-message: var(--background-secondary-alt);

  --tab-outline-color: transparent;
  --divider-color: transparent;

  --prompt-border-color: var(--panel-border-color);
  --modal-border-color: var(--panel-border-color);

  --background-modifier-border-hover: var(--interactive-hover);
  --background-modifier-border-focus: var(--interactive-hover);

  --checkbox-color: var(--text-accent);
  --checkbox-color-hover: var(--text-accent-hover);

  --nav-item-background-active: var(--interactive-accent);
  --nav-item-color-active: #ffffff;

  --tag-color: var(--yellow);
  --tag-background: var(--background-primary-alt);
  --tag-color-hover: var(--yellow);
  --tag-background-hover: var(--background-primary-alt);
  --tag-padding-x: 4px;
  --tag-padding-y: 2px;
  --tag-radius: 4px;

  --inline-title-weight: var(--bold-weight);
  --link-decoration: none;
  --link-external-decoration: none;
  --embed-padding: 0 0 0 var(--size-4-4);
}

/* Search */
.search-result .search-result-file-title {
  cursor: pointer;
}

.search-result .collapse-icon {
  cursor: var(--cursor);
}

.search-result:not(.is-collapsed) .search-result-file-title {
  color: var(--blue);
}

/* File tab separators */
.workspace .mod-root .workspace-tab-header-inner::after {
  right: unset;
  left: -0.5px;
}

.workspace .mod-root .workspace-tab-header:last-child .workspace-tab-header-inner::before {
  position: absolute;
  right: -0.5px;
  width: 1px;
  background-color: var(--tab-divider-color);
  content: '';
  height: 20px;
}

.workspace .mod-root  .workspace-tab-header.is-active .workspace-tab-header-inner::after,
.workspace .mod-root  .workspace-tab-header.is-active .workspace-tab-header-inner::before,
.workspace .mod-root  .workspace-tab-header:first-child .workspace-tab-header-inner::after,
.workspace .mod-root  .workspace-tab-header.is-active + .workspace-tab-header .workspace-tab-header-inner::after {
  opacity: 0;
}

/* Editor and output */
.markdown-rendered blockquote {
  padding: var(--embed-padding);
}

mjx-container {
  text-align: left !important;
}

.math-block {
  font-size: 1.3em;
}

.theme-light :not(pre)>code,
.theme-light pre {
  background: var(--background-primary);
  box-shadow: inset 0 0 0 1px var(--background-primary-alt);
  border-radius: 4px;
}

.markdown-preview-section > div h1,
.markdown-preview-section > div h2,
.markdown-preview-section > div h3,
.markdown-preview-section > div h4,
.markdown-preview-section > div h5,
.markdown-preview-section > div h6 {
  margin-top: 40px;
}

.mod-header + div h1,
.mod-header + div h2,
.mod-header + div h3,
.mod-header + div h4,
.mod-header + div h5,
.mod-header + div h6 {
  margin-top: 30px;
}

.cm-sizer > .inline-title {
  margin-bottom: 20px;
}

/* Miscellaneous */
.theme-dark .dropdown:hover {
  background-color: var(--background-modifier-form-field);
}

.tooltip {
  color: var(--text-muted);
}

.nav-file, .nav-folder {
  padding: 1px 2px;
}

body:not(.is-grabbing) .nav-file-title.is-being-dragged,
body:not(.is-grabbing) .nav-folder-title.is-being-dragged,
.nav-file-title.is-being-dragged,
.nav-folder-title.is-being-dragged {
  background-color: var(--background-primary-alt);
  color: var(--nav-item-color);
}

.view-header-title {
  text-decoration: underline;
  text-decoration-color: var(--text-muted);
  text-underline-offset: 1.5px;
}

.status-bar {
  border-color: var(--panel-border-color);
  border-width: 1px;
  padding: 4px 8px;
}

.theme-dark button.mod-warning {
  --background-modifier-error: #d42020;
  --background-modifier-error-hover: #b01515;
}

.theme-light button.mod-warning {
  --background-modifier-error: #f23f3f;
  --background-modifier-error-hover: #d72020;
}

/* Code syntax highlighting */
code[class*='language-'], pre[class*='language-'] {
  text-align: left !important;
  white-space: pre !important;
  word-spacing: normal !important;
  word-break: normal !important;
  word-wrap: normal !important;
  line-height: 1.5 !important;
  tab-size: 4 !important;
  hyphens: none !important;
}

pre[class*='language-'] {
  /* Code blocks */
  padding: 1em !important;
  margin: .5em 0 !important;
  overflow: auto !important;
}

:not(pre)>code[class*='language-'] {
  /* Inline code */
  padding: .1em !important;
  border-radius: .3em !important;
  white-space: normal !important;
}

.token.comment, .token.prolog, .token.doctype, .token.cdata,
.HyperMD-codeblock .cm-comment {
  color: var(--gray-1) !important;
}

.token.punctuation,
.HyperMD-codeblock .cm-hmd-codeblock, .HyperMD-codeblock .cm-bracket {
  color: var(--gray-2) !important;
}

.token.selector, .token.tag,
.HyperMD-codeblock .cm-tag, .HyperMD-codeblock .cm-property, .HyperMD-codeblock .cm-meta, .HyperMD-codeblock .cm-qualifier, .HyperMD-codeblock .cm-header, .HyperMD-codeblock .cm-quote, .HyperMD-codeblock .cm-hr, .HyperMD-codeblock .cm-link {
  color: var(--red) !important;
}

.token.property, .token.boolean, .token.number, .token.constant, .token.symbol, .token.attr-name, .token.deleted,
.HyperMD-codeblock .cm-number, .HyperMD-codeblock .cm-atom, .HyperMD-codeblock .cm-attribute {
  color: var(--orange) !important;
}

.token.string, .token.char, .token.attr-value, .token.builtin, .token.inserted,
.HyperMD-codeblock .cm-string, .HyperMD-codeblock .cm-builtin {
  color: var(--green) !important;
}

.token.operator, .token.entity, .token.url, .language-css .token.string, .style .token.string,
.HyperMD-codeblock .cm-string-2, .HyperMD-codeblock .cm-operator {
  color: var(--aqua) !important;
}

.token.atrule, .token.keyword,
.HyperMD-codeblock .cm-keyword {
  color: var(--purple) !important;
}

.token.function, .token.macro.property,
.HyperMD-codeblock .cm-def, .HyperMD-codeblock .cm-variable {
  color: var(--blue) !important;
}

.token.class-name,
.HyperMD-codeblock .cm-variable-2, .HyperMD-codeblock .cm-variable-3 {
  color: var(--yellow) !important;
}

.token.regex, .token.important, .token.variable {
  color: var(--purple) !important;
}

.token.important, .token.bold {
  font-weight: bold !important;
}

.token.italic {
  font-style: italic !important;
}

.token.entity {
  cursor: help !important;
}