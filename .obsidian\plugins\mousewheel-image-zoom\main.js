/*
THIS IS A GENERATED/BUNDLED FILE BY ROLLUP
if you want to view the source visit the plugins github repository
*/

'use strict';

var obsidian = require('obsidian');

/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGL<PERSON>ENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

/**
 * ReplaceTerm enables us to store the parameters for a replacement to add a new size parameter.
 */
class ReplaceTerm {
    constructor(replaceFrom, replaceWith) {
        this.replaceFrom = replaceFrom;
        this.replaceWith = replaceWith;
    }
    // Generate a string that can be used in a string.replace() call as the string to replace
    getReplaceFromString(oldSize) {
        return this.replaceFrom(oldSize);
    }
    // Generate a string that can be used in a string.replace() call as the replacement string
    getReplaceWithString(newSize) {
        return this.replaceWith(newSize);
    }
}
class Util {
    /**
         * For a given file content decide if a string is inside a table
         * @param searchString string
         * @param fileValue file content
         * @private
         */
    static isInTable(searchString, fileValue) {
        return fileValue.search(new RegExp(`^\\|.+${escapeRegex(searchString)}.+\\|$`, "m")) !== -1;
    }
    /**
     * Get the image name from a given src uri of a local image
     * (URI like app://local/C:/.../image.png?1677337704730)
     * @param imageUri uri of the image
     * @private
     */
    static getLocalImageNameFromUri(imageUri) {
        imageUri = decodeURI(imageUri);
        const imageNameMatch = imageUri.match(/([^\/?\\]+)(\?.*?|)$/);
        const imageName = imageNameMatch ? imageNameMatch[1] : "";
        // Handle linux not correctly decoding the %2F before the Filename to a \
        const hasLinuxDecodingIssue = imageName.startsWith("2F");
        return hasLinuxDecodingIssue ? imageName.slice(2) : imageName;
    }
    /**
     * Get the parameters needed to handle the zoom for a local image.
     * Source can be either a obsidian link like [[image.png]] or a markdown link like [image.png](image.png)
     * @param imageName Name of the image
     * @param fileText content of the current file
     * @returns parameters to handle the zoom
     */
    static getLocalImageZoomParams(imageName, fileText) {
        imageName = this.determineImageName(imageName, fileText);
        // Get the folder name if the image is located in a folder
        const folderName = this.getFolderNameIfExist(imageName, fileText);
        imageName = `${folderName}${imageName}`;
        const isInTable = Util.isInTable(imageName, fileText);
        // Separator to use for the replacement
        const sizeSeparator = isInTable ? "\\|" : "|";
        // Separator to use for the regex: isInTable ? \\\| : \|
        const regexSeparator = isInTable ? "\\\\\\|" : "\\|";
        // check character before the imageName to check if markdown link or obsidian link
        const imageNamePosition = fileText.indexOf(imageName);
        const isObsidianLink = fileText.charAt(imageNamePosition - 1) === "[";
        if (isObsidianLink) {
            const imageAttributes = this.getImageAttributes(imageName, fileText);
            imageName = `${imageName}${imageAttributes}`;
            return Util.generateReplaceTermForObsidianSyntax(imageName, regexSeparator, sizeSeparator);
        }
        else {
            return Util.generateReplaceTermForMarkdownSyntax(imageName, regexSeparator, sizeSeparator, fileText);
        }
    }
    /**
     * When using markdown link syntax the image name can be encoded. This function checks if the image name is encoded and if not encodes it.
     *
     * @param origImageName Image name
     * @param fileText File content
     * @returns image name with the correct encoding
     */
    static determineImageName(origImageName, fileText) {
        const encodedImageName = encodeURI(origImageName);
        const spaceEncodedImageName = origImageName.replace(/ /g, "%20");
        // Try matching original, full URI encoded, and space encoded
        const imageNameVariants = [origImageName, encodedImageName, spaceEncodedImageName];
        for (const variant of imageNameVariants) {
            if (fileText.includes(variant)) {
                return variant;
            }
        }
        throw new Error("Image not found in file");
    }
    /**
    * Extracts the folder name from the given image name by looking for the first "[" or "(" character
    * that appears before the image name in the file text.
    * @param imageName The name of the image.
    * @param fileText The text of the file that contains the image.
    * @returns The name of the folder that contains the image, or an empty string if no folder is found.
    */
    static getFolderNameIfExist(imageName, fileText) {
        const index = fileText.indexOf(imageName);
        if (index === -1) {
            throw new Error("Image not found in file");
        }
        const stringBeforeFileName = fileText.substring(0, index);
        const lastOpeningBracket = stringBeforeFileName.lastIndexOf("["); // Obsidian link
        const lastOpeningParenthesis = stringBeforeFileName.lastIndexOf("("); // Markdown link
        const lastOpeningBracketOrParenthesis = Math.max(lastOpeningBracket, lastOpeningParenthesis);
        const folderName = stringBeforeFileName.substring(lastOpeningBracketOrParenthesis + 1);
        return folderName;
    }
    /**
* Extracts any image attributes like |ctr for ITS Theme that appear after the given image name in the file.
* @param imageName - The name of the image to search for.
* @param fileText - The content of the file to search in.
* @returns A string containing any image attributes that appear after the image name.
*/
    static getImageAttributes(imageName, fileText) {
        const index = fileText.indexOf(imageName);
        const stringAfterFileName = fileText.substring(index + imageName.length);
        const regExpMatchArray = stringAfterFileName.match(/([^\]]*?)\\?\|\d+]]|([^\]]*?)]]|/);
        if (regExpMatchArray) {
            if (!!regExpMatchArray[1]) {
                return regExpMatchArray[1];
            }
            else if (!!regExpMatchArray[2]) {
                return regExpMatchArray[2];
            }
        }
        return "";
    }
    /**
     * Get the parameters needed to handle the zoom for images in markdown format.
     * Example: ![image.png](image.png)
     * @param imageName Name of the image
     * @param fileText content of the current file
     * @returns parameters to handle the zoom
     * @private
     *
     */
    static generateReplaceTermForMarkdownSyntax(imageName, regexSeparator, sizeSeparator, fileText) {
        const sizeMatchRegExp = new RegExp(`${regexSeparator}(\\d+)]${escapeRegex("(" + imageName + ")")}`);
        const replaceSizeExistFrom = (oldSize) => `${sizeSeparator}${oldSize}](${imageName})`;
        const replaceSizeExistWith = (newSize) => `${sizeSeparator}${newSize}](${imageName})`;
        const replaceSizeNotExistsFrom = (oldSize) => `](${imageName})`;
        const replaceSizeNotExistsWith = (newSize) => `${sizeSeparator}${newSize}](${imageName})`;
        const replaceSizeExist = new ReplaceTerm(replaceSizeExistFrom, replaceSizeExistWith);
        const replaceSizeNotExist = new ReplaceTerm(replaceSizeNotExistsFrom, replaceSizeNotExistsWith);
        return {
            sizeMatchRegExp: sizeMatchRegExp,
            replaceSizeExist: replaceSizeExist,
            replaceSizeNotExist: replaceSizeNotExist,
        };
    }
    /**
     * Get the parameters needed to handle the zoom for images in markdown format.
     * Example: ![[image.png]]
     * @param imageName Name of the image
     * @param fileText content of the current file
     * @returns parameters to handle the zoom
     * @private
     *
     */
    static generateReplaceTermForObsidianSyntax(imageName, regexSeparator, sizeSeparator) {
        const sizeMatchRegExp = new RegExp(`${escapeRegex(imageName)}${regexSeparator}(\\d+)`);
        const replaceSizeExistFrom = (oldSize) => `${imageName}${sizeSeparator}${oldSize}`;
        const replaceSizeExistWith = (newSize) => `${imageName}${sizeSeparator}${newSize}`;
        const replaceSizeNotExistsFrom = (oldSize) => `${imageName}`;
        const replaceSizeNotExistsWith = (newSize) => `${imageName}${sizeSeparator}${newSize}`;
        const replaceSizeExist = new ReplaceTerm(replaceSizeExistFrom, replaceSizeExistWith);
        const replaceSizeNotExist = new ReplaceTerm(replaceSizeNotExistsFrom, replaceSizeNotExistsWith);
        return {
            sizeMatchRegExp: sizeMatchRegExp,
            replaceSizeExist: replaceSizeExist,
            replaceSizeNotExist: replaceSizeNotExist,
        };
    }
    /**
     * Get the parameters needed to handle the zoom for a remote image.
     * Format: https://www.example.com/image.png
     * @param imageUri URI of the image
     * @param fileText content of the current file
     * @returns parameters to handle the zoom
     */
    static getRemoteImageZoomParams(imageUri, fileText) {
        const isInTable = Util.isInTable(imageUri, fileText);
        // Separator to use for the replacement
        const sizeSeparator = isInTable ? "\\|" : "|";
        // Separator to use for the regex: isInTable ? \\\| : \|
        const regexSeparator = isInTable ? "\\\\\\|" : "\\|";
        return Util.generateReplaceTermForMarkdownSyntax(imageUri, regexSeparator, sizeSeparator, fileText);
    }
}
/**
 * Function to escape a string into a valid searchable string for a regex
 * @param string string to escape
 * @returns escaped string
 */
function escapeRegex(string) {
    return string.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
}

var ModifierKey;
(function (ModifierKey) {
    ModifierKey["ALT"] = "AltLeft";
    ModifierKey["CTRL"] = "ControlLeft";
    ModifierKey["SHIFT"] = "ShiftLeft";
    ModifierKey["ALT_RIGHT"] = "AltRight";
    ModifierKey["CTRL_RIGHT"] = "ControlRight";
    ModifierKey["SHIFT_RIGHT"] = "ShiftRight";
})(ModifierKey || (ModifierKey = {}));
const DEFAULT_SETTINGS = {
    modifierKey: ModifierKey.ALT,
    stepSize: 25,
    initialSize: 500,
    resizeInCanvas: true,
};
const CtrlCanvasConflictWarning = "Warning: Using Ctrl as the modifier key conflicts with default canvas zooming behavior when 'Resize in canvas' is enabled. Consider using another modifier key or disabling 'Resize in canvas'.";
class MouseWheelZoomPlugin extends obsidian.Plugin {
    constructor() {
        super(...arguments);
        this.isKeyHeldDown = false;
        this.wheelOpt = { passive: false, capture: true };
        this.wheelEvent = 'wheel';
    }
    onload() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.loadSettings();
            this.registerEvent(this.app.workspace.on("window-open", (newWindow) => this.registerEvents(newWindow.win)));
            this.registerEvents(window);
            this.addSettingTab(new MouseWheelZoomSettingsTab(this.app, this));
            console.log("Loaded: Mousewheel image zoom");
            this.checkExistingUserConflict();
        });
    }
    checkExistingUserConflict() {
        const noticeShownKey = 'mousewheel-zoom-ctrl-warning-shown'; // Key for localStorage flag
        const isCtrl = this.settings.modifierKey === ModifierKey.CTRL || this.settings.modifierKey === ModifierKey.CTRL_RIGHT;
        // Only show the notice if the conflict exists AND the user hasn't dismissed it before (using localStorage flag)
        if (isCtrl && this.settings.resizeInCanvas && !localStorage.getItem(noticeShownKey)) {
            const fragment = document.createDocumentFragment();
            const titleEl = document.createElement('strong');
            titleEl.textContent = "Mousewheel Image Zoom";
            fragment.appendChild(titleEl);
            fragment.appendChild(document.createElement('br'));
            const messageEl = document.createElement('span');
            messageEl.textContent = CtrlCanvasConflictWarning;
            fragment.appendChild(messageEl);
            fragment.appendChild(document.createElement('br'));
            const settingsButton = document.createElement('button');
            settingsButton.textContent = "Open Settings";
            settingsButton.style.marginTop = "5px";
            settingsButton.onclick = () => {
                // settings is a private property of the app object, so we need to cast it to any to access it
                // See https://forum.obsidian.md/t/open-settings-for-my-plugin-community-plugin-settings-deeplink/61563/4
                const setting = this.app.setting;
                setting.open();
                setting.openTabById(this.manifest.id);
            };
            fragment.appendChild(settingsButton);
            new obsidian.Notice(fragment, 0);
            // Set the flag in localStorage so the notice doesn't appear again
            // unless the user clears their localStorage or the key changes.
            localStorage.setItem(noticeShownKey, 'true');
        }
    }
    /**
     * When the config key is released, we enable the scroll again and reset the key held down flag.
     */
    onConfigKeyUp(currentWindow) {
        this.isKeyHeldDown = false;
        this.enableScroll(currentWindow);
    }
    onunload(currentWindow = window) {
        // Re-enable the normal scrolling behavior when the plugin unloads
        this.enableScroll(currentWindow);
    }
    /**
    * Registers image resizing events for the specified window
    * @param currentWindow window in which to register events
    * @private
    */
    registerEvents(currentWindow) {
        const doc = currentWindow.document;
        this.registerDomEvent(doc, "keydown", (evt) => {
            var _a;
            if (evt.code === this.settings.modifierKey.toString()) {
                // When canvas mode is enabled we just ignore the keydown event if the canvas is active
                const isActiveViewCanvas = ((_a = this.app.workspace.getActiveViewOfType(obsidian.View)) === null || _a === void 0 ? void 0 : _a.getViewType()) === "canvas";
                if (isActiveViewCanvas && !this.settings.resizeInCanvas) {
                    return;
                }
                this.isKeyHeldDown = true;
                if (this.settings.modifierKey !== ModifierKey.SHIFT && this.settings.modifierKey !== ModifierKey.SHIFT_RIGHT) { // Ignore shift to allow horizontal scrolling
                    // Disable the normal scrolling behavior when the key is held down
                    this.disableScroll(currentWindow);
                }
            }
        });
        this.registerDomEvent(doc, "keyup", (evt) => {
            if (evt.code === this.settings.modifierKey.toString()) {
                this.onConfigKeyUp(currentWindow);
            }
        });
        this.registerDomEvent(doc, "wheel", (evt) => {
            if (this.isKeyHeldDown) {
                // When for example using Alt + Tab to switch between windows, the key is still recognized as held down.
                // We check if the key is really held down by checking if the key is still pressed in the event when the
                // wheel event is triggered.
                if (!this.isConfiguredKeyDown(evt)) {
                    this.onConfigKeyUp(currentWindow);
                    return;
                }
                const eventTarget = evt.target;
                const targetIsCanvas = eventTarget.hasClass("canvas-node-content-blocker");
                const targetIsCanvasNode = eventTarget.closest(".canvas-node-content") !== null;
                const targetIsImage = eventTarget.nodeName === "IMG";
                if (targetIsCanvas || targetIsCanvasNode || targetIsImage) {
                    this.disableScroll(currentWindow);
                }
                if (targetIsCanvas && this.settings.resizeInCanvas) {
                    // seems we're trying to zoom on some canvas node.                    
                    this.handleZoomForCanvas(evt, eventTarget);
                }
                else if (targetIsCanvasNode) ;
                else if (targetIsImage) {
                    // Handle the zooming of the image
                    this.handleZoom(evt, eventTarget);
                }
            }
        });
        this.registerDomEvent(currentWindow, "blur", () => {
            // When the window loses focus, ensure scrolling is re-enabled for this window
            // and reset the key held state defensively, although the keyup should ideally handle it.
            this.isKeyHeldDown = false;
            this.enableScroll(currentWindow);
        });
    }
    /**
    * Handles zooming with the mousewheel on canvas node
    * @param evt wheel event
    * @param eventTarget targeted canvas node element
    * @private
    */
    handleZoomForCanvas(evt, eventTarget) {
        // get active canvas
        const isCanvas = this.app.workspace.getActiveViewOfType(obsidian.View).getViewType() === "canvas";
        if (!isCanvas) {
            throw new Error("Can't find canvas");
        }
        // Unfortunately the current type definitions don't include any canvas functionality...
        const canvas = this.app.workspace.getActiveViewOfType(obsidian.View).canvas;
        // get triggered canvasNode
        const canvasNode = Array.from(canvas.nodes.values())
            .find(node => node.contentBlockerEl == eventTarget);
        // Adjust delta based on the direction of the resize
        let delta = evt.deltaY > 0 ? this.settings.stepSize : this.settings.stepSize * -1;
        // Calculate new dimensions directly using the delta and aspectRatio
        const aspectRatio = canvasNode.width / canvasNode.height;
        const newWidth = canvasNode.width + delta;
        const newHeight = newWidth / aspectRatio;
        // Resize the canvas node using the new dimensions
        canvasNode.resize({ width: newWidth, height: newHeight });
    }
    /**
     * Handles zooming with the mousewheel on an image
     * @param evt wheel event
     * @param eventTarget targeted image element
     * @private
     */
    handleZoom(evt, eventTarget) {
        return __awaiter(this, void 0, void 0, function* () {
            const imageUri = eventTarget.attributes.getNamedItem("src").textContent;
            const activeFile = yield this.getActivePaneWithImage(eventTarget);
            yield this.app.vault.process(activeFile, (fileText) => {
                let frontmatter = "";
                let body = fileText;
                const frontmatterRegex = /^---\s*([\s\S]*?)\s*---\n*/;
                const match = fileText.match(frontmatterRegex);
                if (match) {
                    frontmatter = match[0]; // Keep the full matched frontmatter block including delimiters and trailing newline
                    body = fileText.slice(frontmatter.length); // The rest is the body
                }
                const zoomParams = this.getZoomParams(imageUri, body, eventTarget);
                // Perform replacements ONLY on the body
                let modifiedBody = body;
                const sizeMatches = body.match(zoomParams.sizeMatchRegExp);
                // Element already has a size entry in the body
                if (sizeMatches !== null) {
                    const oldSize = parseInt(sizeMatches[1]);
                    let newSize = oldSize;
                    if (evt.deltaY < 0) {
                        newSize += this.settings.stepSize;
                    }
                    else if (evt.deltaY > 0 && newSize > this.settings.stepSize) {
                        newSize -= this.settings.stepSize;
                    }
                    // Replace within the body
                    modifiedBody = body.replace(zoomParams.replaceSizeExist.getReplaceFromString(oldSize), zoomParams.replaceSizeExist.getReplaceWithString(newSize));
                }
                else { // Element has no size entry in the body -> give it an initial size
                    const initialSize = this.settings.initialSize;
                    const image = new Image();
                    image.src = imageUri;
                    const width = image.naturalWidth || initialSize;
                    const minWidth = Math.min(width, initialSize);
                    // Replace within the body
                    modifiedBody = body.replace(zoomParams.replaceSizeNotExist.getReplaceFromString(0), zoomParams.replaceSizeNotExist.getReplaceWithString(minWidth));
                }
                // Combine original frontmatter with the modified body
                return frontmatter + modifiedBody;
            });
        });
    }
    /**
     * Loop through all panes and get the pane that hosts a markdown file with the image to zoom
     * @param imageElement The HTML Element of the image
     * @private
     */
    getActivePaneWithImage(imageElement) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise(((resolve, reject) => {
                this.app.workspace.iterateAllLeaves(leaf => {
                    if (leaf.view.containerEl.contains(imageElement) && leaf.view instanceof obsidian.MarkdownView) {
                        resolve(leaf.view.file);
                    }
                });
                reject(new Error("No file belonging to the image found"));
            }));
        });
    }
    getZoomParams(imageUri, fileText, target) {
        if (imageUri.contains("http")) {
            return Util.getRemoteImageZoomParams(imageUri, fileText);
        }
        else if (target.classList.value.match("excalidraw-svg.*")) {
            const src = target.attributes.getNamedItem("filesource").textContent;
            // remove ".md" from the end of the src
            const imageName = src.substring(0, src.length - 3);
            // Only get text after "/"
            const imageNameAfterSlash = imageName.substring(imageName.lastIndexOf("/") + 1);
            return Util.getLocalImageZoomParams(imageNameAfterSlash, fileText);
        }
        else if (imageUri.contains("app://")) {
            const imageName = Util.getLocalImageNameFromUri(imageUri);
            return Util.getLocalImageZoomParams(imageName, fileText);
        }
        else if (imageUri.contains("data:image/")) { // for image generated by PDF++ extension
            // example: data:image/png;base64,iVB...
            const imageName = Util.getLocalImageNameFromUri(target.parentElement.getAttribute("src"));
            return Util.getLocalImageZoomParams(imageName, fileText);
        }
        throw new Error("Image is not zoomable");
    }
    loadSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            this.settings = Object.assign({}, DEFAULT_SETTINGS, yield this.loadData());
        });
    }
    saveSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.saveData(this.settings);
        });
    }
    // Utilities to disable and enable scrolling //
    preventDefault(ev) {
        ev.preventDefault();
    }
    /**
     * Disables the normal scroll event
     */
    disableScroll(currentWindow) {
        currentWindow.addEventListener(this.wheelEvent, this.preventDefault, this.wheelOpt);
    }
    /**
     * Enables the normal scroll event
     */
    enableScroll(currentWindow) {
        currentWindow.removeEventListener(this.wheelEvent, this.preventDefault, this.wheelOpt);
    }
    isConfiguredKeyDown(evt) {
        switch (this.settings.modifierKey) {
            case ModifierKey.ALT:
            case ModifierKey.ALT_RIGHT:
                return evt.altKey;
            case ModifierKey.CTRL:
            case ModifierKey.CTRL_RIGHT:
                return evt.ctrlKey;
            case ModifierKey.SHIFT:
            case ModifierKey.SHIFT_RIGHT:
                return evt.shiftKey;
        }
    }
}
class MouseWheelZoomSettingsTab extends obsidian.PluginSettingTab {
    constructor(app, plugin) {
        super(app, plugin);
        this.plugin = plugin;
    }
    // Helper function to update the warning message
    updateWarningMessage(modifierKey, resizeInCanvas) {
        if (!this.warningEl)
            return;
        const isCtrl = modifierKey === ModifierKey.CTRL || modifierKey === ModifierKey.CTRL_RIGHT;
        const conflict = isCtrl && resizeInCanvas;
        if (conflict) {
            this.warningEl.setText(CtrlCanvasConflictWarning);
            this.warningEl.style.display = 'block';
            this.warningEl.style.color = 'var(--text-warning)';
            this.warningEl.style.marginTop = '10px';
        }
        else {
            this.warningEl.setText("");
            this.warningEl.style.display = 'none';
        }
    }
    display() {
        let { containerEl } = this;
        containerEl.empty();
        containerEl.createEl('h2', { text: 'Settings for mousewheel zoom' });
        new obsidian.Setting(containerEl)
            .setName('Trigger Key')
            .setDesc('Key that needs to be pressed down for mousewheel zoom to work.')
            .addDropdown(dropdown => dropdown
            .addOption(ModifierKey.CTRL, "Ctrl")
            .addOption(ModifierKey.ALT, "Alt")
            .addOption(ModifierKey.SHIFT, "Shift")
            .addOption(ModifierKey.CTRL_RIGHT, "Right Ctrl")
            .addOption(ModifierKey.ALT_RIGHT, "Right Alt")
            .addOption(ModifierKey.SHIFT_RIGHT, "Right Shift")
            .setValue(this.plugin.settings.modifierKey)
            .onChange((value) => __awaiter(this, void 0, void 0, function* () {
            this.plugin.settings.modifierKey = value;
            this.updateWarningMessage(this.plugin.settings.modifierKey, this.plugin.settings.resizeInCanvas);
            yield this.plugin.saveSettings();
        })));
        new obsidian.Setting(containerEl)
            .setName('Step size')
            .setDesc('Step value by which the size of the image should be increased/decreased')
            .addSlider(slider => {
            slider
                .setValue(25)
                .setLimits(0, 100, 1)
                .setDynamicTooltip()
                .setValue(this.plugin.settings.stepSize)
                .onChange((value) => __awaiter(this, void 0, void 0, function* () {
                this.plugin.settings.stepSize = value;
                yield this.plugin.saveSettings();
            }));
        });
        new obsidian.Setting(containerEl)
            .setName('Initial Size')
            .setDesc('Initial image size if no size was defined beforehand')
            .addSlider(slider => {
            slider
                .setValue(500)
                .setLimits(0, 1000, 25)
                .setDynamicTooltip()
                .setValue(this.plugin.settings.initialSize)
                .onChange((value) => __awaiter(this, void 0, void 0, function* () {
                this.plugin.settings.initialSize = value;
                yield this.plugin.saveSettings();
            }));
        });
        new obsidian.Setting(containerEl)
            .setName('Resize in canvas')
            .setDesc('When enabled, all nodes on the Obsidian canvas can also be resized using the Modifier key')
            .addToggle((toggle) => {
            toggle.setValue(this.plugin.settings.resizeInCanvas)
                .onChange((value) => __awaiter(this, void 0, void 0, function* () {
                this.plugin.settings.resizeInCanvas = value;
                this.updateWarningMessage(this.plugin.settings.modifierKey, value);
                yield this.plugin.saveSettings();
            }));
        });
        this.warningEl = containerEl.createDiv({ cls: 'mousewheel-zoom-warning' });
        this.warningEl.style.display = 'none';
        this.updateWarningMessage(this.plugin.settings.modifierKey, this.plugin.settings.resizeInCanvas);
    }
}

module.exports = MouseWheelZoomPlugin;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
