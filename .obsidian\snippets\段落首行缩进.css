/* from blue topaz */
.markdown-source-view.mod-cm6 div.cm-line:not(.HyperMD-header, .hr),
.markdown-source-view.mod-cm6.indent div.cm-line:not(.HyperMD-header, .hr) {
  text-indent: 2em;
}

.markdown-source-view.mod-cm6 div.cm-line:not(.HyperMD-header, .hr) .cm-hmd-frontmatter:first-of-type,
.markdown-source-view.mod-cm6.indent div.cm-line:not(.HyperMD-header, .hr) .cm-hmd-frontmatter:first-of-type {
 margin-left: -2em;
}
.markdown-source-view.mod-cm6 div.has-banner.cm-line:not(.HyperMD-header, .hr) .cm-def.cm-hmd-frontmatter,
.markdown-source-view.mod-cm6 div.has-banner.cm-line:not(.HyperMD-header, .hr) .collapse-indicator,
.markdown-source-view.mod-cm6.indent div.has-banner.cm-line:not(.HyperMD-header, .hr) .cm-def.cm-hmd-frontmatter,
.markdown-source-view.mod-cm6.indent div.has-banner.cm-line:not(.HyperMD-header, .hr) .collapse-indicator {
  margin-left: 0;
  left: -3em;
}

[data-type="markdown"] div[class="el-p"]:not(blockquote) > p,
[data-type="markdown"] :is(.markdown-preview-view,.markdown-rendered).indent div[class="el-p"]:not(blockquote) > p {
  text-indent: 2em;
}


[data-type="markdown"] div[class="el-p"]:not(blockquote) > p>br,
[data-type="markdown"] :is(.markdown-preview-view,.markdown-rendered).indent div[class="el-p"]:not(blockquote) >  p>br {
  content: ' ';
  white-space: pre;
  line-height: calc((var(--paragraph-spacing) + 0.3)*1em);
  display:unset;
}
[data-type="markdown"] div[class="el-p"]:not(blockquote) > p>br::after,
[data-type="markdown"] :is(.markdown-preview-view,.markdown-rendered).indent div[class="el-p"]:not(blockquote) >p>br::after {
  content: '\A\0009\0009';
}

.print *:not(blockquote) > p {
    text-indent: 2em;
}
.print *:not(blockquote) > p>br {
  content: ' ';
  white-space: pre;
  line-height: calc((var(--paragraph-spacing) + 0.3)*1em);
  display:unset;
}
.print *:not(blockquote) > p>br::after {
  content: '\A\0009\0009';
}