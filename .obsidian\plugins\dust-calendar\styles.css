
.d-header-font {
    font-size: calc((15px + var(--d-font-size-factor) * 5px) * var(--d-font-size-custom-enable) + (10px + 0.5 * var(--font-text-size)) * var(--d-font-size-follow-obsidian));
}

.d-normal-font, .d-bold-font, .month-view-header-item, .month-view-week-index-item, .year-view-month-item, .year-view-quarter-item, .today-label, .view-selector-label {
    font-size: calc((12px + var(--d-font-size-factor) * 4px) * var(--d-font-size-custom-enable) + (8px + 0.4 * var(--font-text-size)) * var(--d-font-size-follow-obsidian));
}

.d-bold-font, .month-view-header-item, .month-view-week-index-item {
    font-weight: bold;
}

.d-script-font {
    font-size: calc((8px + var(--d-font-size-factor) * 2px) * var(--d-font-size-custom-enable) + (6px + 0.2 * var(--font-text-size)) * var(--d-font-size-follow-obsidian));
}

.d-setting-accent {
    color: var(--text-accent);
}

.d-setting-error {
    color: var(--text-error);
}

.d-icon {
    width: 1em;
    height: 1em;
}

.d-icon:hover {
    color: var(--interactive-accent);
}

.d-unselected-item, .calendar-header-content-year, .calendar-header-content-month {
    color: var(--text-normal);
}

.d-unselected-item:hover, .calendar-header-content-year:hover, .calendar-header-content-month:hover {
    background-color: var(--interactive-hover);
}

.d-selected-item {
    color: var(--text-on-accent);
    background-color: var(--interactive-accent);
}

.d-selected-item:hover {
    background-color: var(--interactive-accent-hover);
}

.calendar-header-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.calendar-header-row {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
}

.calendar-header-block, .calendar-header-block-year, .calendar-header-block-quarter, .calendar-header-block-month {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    text-align: center;
    min-width: 40px;
    border-radius: 4px;
}

.calendar-header-block-label {
    display: flex;
    justify-content: space-around;
    width: 5em;
    align-items: center;
}

.calendar-header-body, .calendar-header-body-year, .calendar-header-body-quarter, .calendar-header-body-month {
    display: flex;
    align-items: center;
}

.calendar-header-content-year, .calendar-header-content-month {
    border-radius: 4px;
    width: 4em;
}

.calendar-header-content-quarter {
    border-radius: 4px;
    width: 3em;
}

.calendar-view-body {
    width: 100%;
    padding-top: 15px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.month-view-header {
    margin-bottom: 6px;
}

.calendar-view-row, .month-view-header {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
}

.calendar-view-item, .month-view-header-item, .month-view-week-index-item, .month-view-day-item, .year-view-month-item, .year-view-quarter-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 25px;
    border-radius: 2px;
}

.year-view-month-item, .year-view-quarter-item {
    width: 3em;
}

.calendar-view-item-body {
    font-size: 12px;
}

.month-view-today, .month-view-special-date {
    color: var(--text-accent);
}

.month-view-today-selected {
}

.month-view-other-month {
    opacity: 0.2;
}

.month-view-work {
    color: var(--text-error);
}

.month-view-rest {
    color: var(--text-success);
}

.statistic-label {
    max-width: calc((16px + var(--d-font-size-factor) * 4px) * var(--d-font-size-custom-enable) + (12px + 0.4 * var(--font-text-size)) * var(--d-font-size-follow-obsidian));
    min-height: 8px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.statistic-label-dot, .statistic-label-hole, .statistic-label-placeholder {
    height: 6px;
    width: 6px;
    margin: 1px;
}

.statistic-label-dot {
    fill: var(--text-normal);
}

.statistic-label-dot-with-todo {
    fill: var(--text-warning);
}

.statistic-label-hole {
    fill: none;
    stroke: var(--text-normal);
}

.circular-label, .today-label, .view-selector-label {
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5em;
    text-align: center;
    border-radius: 50%;
}
