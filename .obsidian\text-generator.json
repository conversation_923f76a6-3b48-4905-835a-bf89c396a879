{"packagesHash": {"default": {"packageId": "default", "name": "Default Prompts Package", "version": "0.0.9", "minTextGeneratorVersion": "0.5.0", "description": "This is the main package that comes with Text Generator plugin in Obsidian", "author": "<PERSON><PERSON><PERSON>", "tags": "writing, brainstorming", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/gpt-3-prompt-templates"}, "dalle": {"packageId": "dalle", "name": "OpenAI Dalle Package", "version": "0.1.1", "minTextGeneratorVersion": "0.7.0", "description": "The package contains some interessting Dalle-2/Dalle-3 prompt templates", "author": "<PERSON><PERSON><PERSON>", "tags": "photo, dalle-2, dalle-3", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/tg-dalle-package"}, "huggingface": {"packageId": "huggingface", "name": "Huggingface Prompts Package", "version": "0.0.4", "minTextGeneratorVersion": "0.5.0", "description": "Huggingface Prompts comes with Text Generator plugin in Obsidian", "author": "<PERSON><PERSON><PERSON>", "tags": "writing, brainstorming, huggingface", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/huggingface"}, "awesomePrompts": {"packageId": "awesomePrompts", "name": "Awesome Prompts", "version": "0.0.3", "minTextGeneratorVersion": "0.5.7", "description": "This repo includes ChatGPT prompt curation to use ChatGPT better.", "author": "f", "tags": "writing, brainstorming, awesome", "authorUrl": "https://github.com/f/awesome-chatgpt-prompts", "repo": "text-gen/awesome-tg-package"}, "tts": {"packageId": "tts", "name": "Text To Speech Package", "version": "0.0.3", "minTextGeneratorVersion": "0.6.0", "description": "Contains Text To Speech Templates and support for TTS for other templates", "author": "Noureddine", "tags": "TTS, Speak", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/TTS"}, "vision": {"packageId": "vision", "name": "Vision Package", "version": "0.0.2", "minTextGeneratorVersion": "0.6.0", "description": "Contains Vision Templates and support for Vision for other templates (Scripts)", "author": "Noureddine", "tags": "OpenAI,markdown,gpt-4-vision,vision,images", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/tg-vision"}, "smartConnections": {"packageId": "smartConnections", "name": "Smart Connections Package", "version": "0.0.1", "minTextGeneratorVersion": "0.6.0", "description": "Contains Smart Connection Templates and support for Smart Connections for other templates (Script)", "author": "Noureddine", "tags": "smartConnections,smart-connections", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/tg-smartConnections"}, "Experimental": {"packageId": "Experimental", "name": "Experimental Package", "version": "0.0.2", "minTextGeneratorVersion": "0.6.6", "description": "Contains experimental templates", "author": "Noureddine", "tags": "experiments", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/experimental-package"}, "testExtension": {"core": true, "type": "feature", "packageId": "testExtension", "name": "test extension Package", "version": "0.0.1", "minTextGeneratorVersion": "0.1.0", "description": "testing extension package", "author": "<PERSON><PERSON><PERSON>", "tags": "testing", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/gpt-3-prompt-templates", "folderName": "testExtension", "price": 20, "installed": false}, "excalidraw": {"packageId": "excalidraw", "name": "excalidraw package", "version": "0.0.1", "minTextGeneratorVersion": "0.6.0", "description": "Contains Excalidraw Templates and support for Excalidraw for other templates (Scripts)", "author": "Noureddine", "tags": "OpenAI,markdown,gpt-4-vision,vision,images", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/tg-excalidraw", "core": true, "folderName": "excalidrawPackage", "price": 2}}, "resources": {}, "installedPackagesHash": {"default": {"packageId": "default", "prompts": [{"promptId": "getEmailNeg"}, {"promptId": "getEmailPos"}, {"promptId": "getIdeas"}, {"promptId": "getOutline"}, {"promptId": "getParagraph"}, {"promptId": "getTags"}, {"promptId": "get<PERSON>itles"}, {"promptId": "rewrite"}, {"promptId": "simplify"}, {"promptId": "summarize"}, {"promptId": "summarizeLarge"}], "installedPrompts": [{"promptId": "summarize", "version": ""}, {"promptId": "summarizeLarge", "version": ""}, {"promptId": "getParagraph", "version": ""}, {"promptId": "simplify", "version": ""}, {"promptId": "getOutline", "version": ""}, {"promptId": "get<PERSON>itles", "version": ""}, {"promptId": "getEmailNeg", "version": ""}, {"promptId": "rewrite", "version": ""}, {"promptId": "getTags", "version": ""}, {"promptId": "getEmailPos", "version": ""}, {"promptId": "getIdeas", "version": ""}], "version": "0.0.5"}}, "subscriptions": []}