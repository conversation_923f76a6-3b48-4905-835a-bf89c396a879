{"minimal-advanced@@hide-markdown": false, "yaozhuwa@css@@code-line-number": false, "anuppuccin-theme-settings@@anuppuccin-theme-light": "ctp-latte", "anuppuccin-theme-settings@@anuppuccin-light-theme-accents": "ctp-accent-light-sapphire", "anuppuccin-theme-settings@@anuppuccin-theme-accents": "ctp-accent-teal", "anuppuccin-theme-settings@@anp-active-line": "anp-current-line-border-only", "Mo-Table@@tbMH-ds": "tbMH-ds-bt", "Mo-Table@@tbMH-yangshi": "tbMH-zhi", "yaozhuwa@css@@table-line-number": false, "yaozhuwa@css@@full-width-table": true, "anuppuccin-theme-settings@@anp-autohide-titlebar": false, "anuppuccin-theme-settings@@anp-cursor": "pointer", "anuppuccin-theme-settings@@anp-toggle-preview": false, "anuppuccin-theme-settings@@bold-weight": "900", "anuppuccin-theme-settings@@anp-decoration-toggle": false, "yaozhuwa@css@@center-image": true, "Mo-Table@@tbMH-qkms": "tbMH-fqk", "Mo-Table@@tbMH-kj-color@@dark": "#060A00", "minimal-style@@row-lines": true, "minimal-style@@col-lines": true, "minimal-style@@table-lines": true, "minimal-style@@row-alt": true, "minimal-style@@col-alt": true, "minimal-style@@table-tabular": true, "minimal-style@@table-numbers": false, "minimal-style@@row-hover": true, "minimal-style@@table-nowrap": false, "minimal-advanced@@font-ui-small": 14, "yaozhuwa@css@@outline-tweak": true, "yaozhuwa@css@@increase-paragraph-gap": false, "yaozhuwa@css@@increase-headings-gap": false, "blue-topaz-theme@@color-scheme-options": "color-scheme-options-simplicity-topaz", "blue-topaz-theme@@background-settings-workplace-background-image": true, "blue-topaz-theme@@background-image-settings-switch": false, "blue-topaz-theme@@background-notebook-liked-switch": true, "blue-topaz-theme@@toggle-split-note-background": false, "blue-topaz-theme@@background-image-settings-command-palette-switch": false, "blue-topaz-theme@@toggle-bg-panel-page": false, "blue-topaz-theme@@left-ribbon-style": "bt-bubble-ribbon", "blue-topaz-theme@@clutter-free-headings": true, "blue-topaz-theme@@toggle-calendar-shadow": false, "blue-topaz-theme@@toggle-calendar-transparent": false, "blue-topaz-theme@@style-options-for-calendar-plugin": "style-options-for-calendar-plugin-style-two", "Blue-Topaz-Codebox-Highlight@@code-theme-selection": "code-theme-sublime", "Blue-Topaz-Codebox-Highlight@@code-box-style-option": "codebox-default-style", "Blue-Topaz-Codebox-Highlight@@whole-code-wrap": true, "blue-topaz-theme@@table-format-options": "default-table", "blue-topaz-theme@@show-border-table": true, "blue-topaz-theme@@full-width-table": false, "blue-topaz-theme@@table-style-options": "table-style-default", "blue-topaz-theme@@file-bg-shape-option": "file-shape-default", "blue-topaz-theme@@bt-colorful-titlebar": true, "blue-topaz-theme@@nowrap-outline": false, "blue-topaz-theme@@toggle-left-aligned-content": true, "blue-topaz-theme@@bt-status-on": true, "blue-topaz-theme@@background-settings-workplace-theme-light": "background-settings-workplace-waves2-light", "blue-topaz-theme@@notebook-liked-markdown-page-options": "notebook-liked-markdown-page-grid-notebook-1", "blue-topaz-theme@@toggle-fixed-pattern": false, "blue-topaz-theme@@layout-style-options": "layout-style-options-default", "blue-topaz-theme@@file-line-width": 70, "blue-topaz-theme@@toggle-paragraph-spacing": false, "Mo-Table@@tbMH-zdykd-jz": 50, "blue-topaz-theme@@table-width": "table-width-auto", "blue-topaz-theme@@toggle-table-transition": false, "blue-topaz-theme@@resizable-mermaid": true, "sidenote-callout@@hide-sidenote-callout-fold-icon": true, "sidenote-callout@@sidenote-backgound": true, "sidenote-callout@@top-sidenote-callout-title": true, "sidenote-callout@@top-left-sidenote-callout-title-position": "l-left-callout-title"}