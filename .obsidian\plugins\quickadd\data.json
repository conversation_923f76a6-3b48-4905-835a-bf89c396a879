{"choices": [{"id": "36d59b83-d472-455a-aeb0-703ade007db2", "name": "日记模板", "type": "Template", "command": true, "templatePath": "Templates/每日笔记模板.md", "fileNameFormat": {"enabled": true, "format": "{{date}}"}, "folder": {"enabled": true, "folders": ["日记/每日笔记"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "dfdc4f6e-aba7-490c-b622-4d04a84c4b3c", "name": "威胁感知运维笔记", "type": "Template", "command": true, "templatePath": "Templates/运维笔记模板.md", "fileNameFormat": {"enabled": true, "format": "{{date}}"}, "folder": {"enabled": true, "folders": ["梆梆安全/1 建设银行/建行产品分类/安全监测平台（威胁感知）/运维日志"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "1be71ecb-d857-42a0-a4a2-90e87a78c353", "name": "部署任务", "type": "Template", "command": true, "templatePath": "Templates/部署任务模板.md", "fileNameFormat": {"enabled": true, "format": "{客户名称}-{产品名}"}, "folder": {"enabled": true, "folders": ["梆梆安全/2 部署任务"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "preview", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": true}], "macros": [], "inputPrompt": "single-line", "devMode": false, "templateFolderPath": "", "announceUpdates": true, "version": "1.13.3", "disableOnlineFeatures": true, "enableRibbonIcon": false, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "text-davinci-003", "maxTokens": 4096}, {"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "gpt-4-turbo", "maxTokens": 128000}, {"name": "gpt-4o", "maxTokens": 128000}, {"name": "gpt-4o-mini", "maxTokens": 128000}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}