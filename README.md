# 项目介绍

本项目用于个人笔记的记录与管理，采用 Obsidian 作为本地笔记编辑工具，并通过 GitLab 仓库进行版本管理和同步。适合需要跨设备、版本可控的知识管理场景。

# 运行环境

- 操作系统：Windows 10 及以上
- Obsidian（建议使用最新版）
- Git 工具（如 Git for Windows）
- GitLab 账号及对应仓库

# 维护与同步步骤

1. 克隆仓库到本地  
   使用 Git 工具将本仓库克隆到本地目录，例如：
   ```bash
   git clone <你的GitLab仓库地址>
   ```

2. 用 Obsidian 打开本地笔记文件夹  
   启动 Obsidian，选择"打开文件夹作为库"，并选择刚刚克隆下来的仓库目录。

3. 日常笔记编辑  
   直接在 Obsidian 中进行笔记的增删改查，Obsidian 会自动保存更改到本地文件。

4. 提交与同步笔记到 GitLab  
   - 打开命令行，进入笔记仓库目录
   - 执行以下命令将更改提交并推送到远程仓库：
     ```bash
     git add .
     git commit -m "更新笔记"
     git push
     ```

5. 从其他设备同步笔记  
   - 在其他设备上同样克隆仓库并用 Obsidian 打开
   - 每次编辑前，先执行 `git pull` 获取最新内容
   - 编辑后重复第4步提交和推送

# 注意事项

- 建议定期推送和拉取，避免版本冲突
- 如遇冲突，优先通过 Obsidian 解决内容，再用 Git 工具合并
- 可结合 GitLab 的私有仓库保护个人隐私

# 插件与设置说明

本项目已集成多种 Obsidian 插件及个性化设置，所有相关配置均保存在仓库的 `.obsidian` 文件夹中，随仓库一同同步。

## 已集成插件（部分示例）

- 插件A（如：日历、任务管理等）
- 插件B
- 插件C
- ...（如需详细列出可补充）

## 设置与插件的同步方式

- `.obsidian` 文件夹包含了所有工作区设置、插件配置及主题等内容。
- 通过 Git 同步仓库时，相关设置和插件配置也会一同同步。

## 新环境下的还原步骤

1. 按前述步骤克隆仓库并用 Obsidian 打开。
2. Obsidian 会自动读取 `.obsidian` 文件夹中的设置和插件信息。
3. 如遇插件未自动安装，可在 Obsidian 的"设置-社区插件"中手动安装缺失插件，配置会自动应用。

> 建议：如有特殊插件需手动安装或需 API Key 等，请在此处补充说明。
